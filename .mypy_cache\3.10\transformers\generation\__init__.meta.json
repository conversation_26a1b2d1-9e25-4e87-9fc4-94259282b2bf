{"data_mtime": 1752081391, "dep_lines": [195, 203, 211, 212, 213, 219, 220, 254, 264, 281, 295, 312, 332, 348, 17, 15, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 30, 30, 30], "dependencies": ["transformers.generation.configuration_utils", "transformers.generation.streamers", "transformers.generation.beam_constraints", "transformers.generation.beam_search", "transformers.generation.candidate_generator", "transformers.generation.continuous_batching", "transformers.generation.logits_process", "transformers.generation.stopping_criteria", "transformers.generation.utils", "transformers.generation.watermarking", "transformers.generation.tf_logits_process", "transformers.generation.tf_utils", "transformers.generation.flax_logits_process", "transformers.generation.flax_utils", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc", "transformers.utils.import_utils"], "hash": "01ece25540aa2be96a59b3cc6d8e4bcdfd83ddf3", "id": "transformers.generation", "ignore_all": true, "interface_hash": "a4dbc996925fc9d63a2bb43f000140d2588920cd", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\generation\\__init__.py", "plugin_data": null, "size": 12472, "suppressed": [], "version_id": "1.16.1"}