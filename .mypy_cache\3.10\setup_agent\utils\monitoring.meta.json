{"data_mtime": 1752081346, "dep_lines": [5, 7, 8, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [10, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["time", "threading", "logging", "typing", "dataclasses", "collections", "functools", "builtins", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "abc", "enum", "types"], "hash": "4f91976c4767fba16168c259936a3d878e7896a0", "id": "setup_agent.utils.monitoring", "ignore_all": false, "interface_hash": "a71a7d206ea713e4e000e4f85dee16f7037c8672", "mtime": 1752080365, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\utils\\monitoring.py", "plugin_data": null, "size": 10852, "suppressed": ["psutil"], "version_id": "1.16.1"}