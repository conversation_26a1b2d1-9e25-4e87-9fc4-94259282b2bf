{"data_mtime": 1752082344, "dep_lines": [7, 8, 34, 5, 6, 45, 46, 1, 1, 1, 1, 1, 1, 1, 1, 51], "dep_prios": [5, 5, 20, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["setup_agent.core.config", "setup_agent.core.exceptions", "utils.embeddings", "logging", "typing", "sys", "pathlib", "builtins", "_frozen_importlib", "abc", "os", "setup_agent.core", "types", "typing_extensions", "utils"], "hash": "2de35967ebf5d37748909f3a04139ab06d9acbf0", "id": "setup_agent.memory.lazy_embeddings", "ignore_all": false, "interface_hash": "c2b3acae12f3baba561eb098c8f238bddbd73a7c", "mtime": 1752082202, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\memory\\lazy_embeddings.py", "plugin_data": null, "size": 6144, "suppressed": ["embeddings"], "version_id": "1.16.1"}