{"data_mtime": 1752081394, "dep_lines": [10, 14, 10, 14, 10, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 20, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["torch._<PERSON>._onnx", "torch.onnx._constants", "torch._C", "torch.onnx", "torch", "builtins", "_frozen_importlib", "abc", "enum", "types", "typing"], "hash": "2e9958b8e0fe8e3c986277b97b16fb64b40a1651", "id": "torch.onnx._globals", "ignore_all": true, "interface_hash": "0998cb8c687aebb7b73a9b6a7342bf86319b808e", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_globals.py", "plugin_data": null, "size": 3091, "suppressed": [], "version_id": "1.16.1"}