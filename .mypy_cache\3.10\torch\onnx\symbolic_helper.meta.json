{"data_mtime": 1752081394, "dep_lines": [20, 14, 18, 18, 18, 18, 19, 20, 14, 18, 24, 26, 2, 4, 5, 6, 7, 8, 9, 11, 13, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 20, 10, 20, 25, 25, 5, 10, 10, 10, 10, 5, 10, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.jit_utils", "torch._<PERSON>._onnx", "torch.onnx._constants", "torch.onnx._type_utils", "torch.onnx.errors", "torch.onnx.utils", "torch.onnx._globals", "torch.onnx._internal", "torch._C", "torch.onnx", "collections.abc", "torch.types", "__future__", "functools", "inspect", "math", "sys", "typing", "warnings", "typing_extensions", "torch", "builtins", "_frozen_importlib", "abc", "enum", "torch._tensor"], "hash": "d068d8cc1c9ddd6d608e0a3baca7fe15bf6b464a", "id": "torch.onnx.symbolic_helper", "ignore_all": true, "interface_hash": "7ed3190ba931e2fc1cd423d2208c06f74ae0ed3a", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\symbolic_helper.py", "plugin_data": null, "size": 84578, "suppressed": [], "version_id": "1.16.1"}