{"data_mtime": 1752081392, "dep_lines": [32, 23, 29, 30, 31, 19, 21, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.utils", "transformers.audio_utils", "transformers.feature_extraction_sequence_utils", "transformers.feature_extraction_utils", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "collections", "enum", "logging", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "cac56f085eaa6f6f96fb5a6888073bd3ff6adad8", "id": "transformers.models.seamless_m4t.feature_extraction_seamless_m4t", "ignore_all": true, "interface_hash": "88b003a91ec818cadb1b4ad034dad8936610e205", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\seamless_m4t\\feature_extraction_seamless_m4t.py", "plugin_data": null, "size": 13628, "suppressed": [], "version_id": "1.16.1"}