{"data_mtime": 1752081393, "dep_lines": [17, 17, 25, 10, 12, 13, 15, 16, 17, 1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["huggingface_hub.utils.logging", "huggingface_hub.utils.tqdm", "huggingface_hub.utils._deprecation", "urllib.parse", "huggingface_hub.constants", "huggingface_hub.repocard", "huggingface_hub.hf_api", "huggingface_hub.lfs", "huggingface_hub.utils", "atexit", "os", "re", "subprocess", "threading", "time", "contextlib", "pathlib", "typing", "huggingface_hub", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "huggingface_hub.utils._auth", "huggingface_hub.utils._subprocess", "huggingface_hub.utils._validators", "logging", "ntpath", "types", "typing_extensions", "urllib"], "hash": "b3e50122191733dcab7c1dcfc0c1d30b21beaf0e", "id": "huggingface_hub.repository", "ignore_all": true, "interface_hash": "68685650e799391442337705467ce2163ca42aa2", "mtime": 1752005469, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\huggingface_hub\\repository.py", "plugin_data": null, "size": 54557, "suppressed": [], "version_id": "1.16.1"}