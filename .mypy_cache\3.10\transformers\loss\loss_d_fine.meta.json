{"data_mtime": 1752081393, "dep_lines": [18, 21, 24, 17, 20, 28, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.functional", "transformers.loss.loss_for_object_detection", "transformers.loss.loss_rt_detr", "torch.nn", "transformers.utils", "transformers.image_transforms", "torch", "builtins", "_frozen_importlib", "abc", "enum", "functools", "torch._C", "torch._C._VariableFunctions", "torch._jit_internal", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.jit", "torch.nn.modules", "torch.nn.modules.loss", "torch.nn.modules.module", "torch.nn.parameter", "torch.utils", "torch.utils._contextlib", "transformers.utils.generic", "transformers.utils.import_utils", "types", "typing"], "hash": "37a99a29db44604c4a1d06c782d814cb4410ae8b", "id": "transformers.loss.loss_d_fine", "ignore_all": true, "interface_hash": "c2e447bc34e38134b754d8c49e9ea967dd668822", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\loss\\loss_d_fine.py", "plugin_data": null, "size": 15881, "suppressed": [], "version_id": "1.16.1"}