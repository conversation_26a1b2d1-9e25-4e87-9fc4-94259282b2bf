{"data_mtime": 1752082282, "dep_lines": [25, 19, 5, 6, 7, 8, 9, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["setup_agent.core.config_schema", "cryptography.fernet", "os", "json", "logging", "typing", "pathlib", "dotenv", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "cryptography", "dotenv.main", "io", "json.decoder", "types", "typing_extensions"], "hash": "788d3b53443619023c117c7f8cb977bac0b79ef7", "id": "setup_agent.core.config", "ignore_all": false, "interface_hash": "72175b7e30c06dfd618038d898df3b7432edaa2e", "mtime": 1752082436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "F:\\SetupAgent\\setup_agent\\core\\config.py", "plugin_data": null, "size": 11742, "suppressed": [], "version_id": "1.16.1"}