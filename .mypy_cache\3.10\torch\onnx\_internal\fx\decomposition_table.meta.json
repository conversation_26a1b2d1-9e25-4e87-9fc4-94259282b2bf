{"data_mtime": 1752081394, "dep_lines": [11, 11, 9, 10, 4, 6, 8, 1, 1, 1], "dep_prios": [10, 20, 10, 10, 5, 5, 10, 5, 30, 30], "dependencies": ["torch.onnx._internal.fx.registration", "torch.onnx._internal.fx", "torch._ops", "torch.fx", "__future__", "typing", "torch", "builtins", "_frozen_importlib", "abc"], "hash": "504e08ff7d61f75070fea5bb362aa62209ea12ec", "id": "torch.onnx._internal.fx.decomposition_table", "ignore_all": true, "interface_hash": "6b42b2cb964c5dceb1595982a5fb99ad0dc55e78", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\fx\\decomposition_table.py", "plugin_data": null, "size": 5242, "suppressed": [], "version_id": "1.16.1"}