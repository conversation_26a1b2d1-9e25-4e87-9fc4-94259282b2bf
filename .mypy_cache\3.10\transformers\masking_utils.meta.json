{"data_mtime": 1752081394, "dep_lines": [28, 19, 23, 24, 37, 19, 21, 22, 15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 20, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.attention.flex_attention", "torch.nn.functional", "transformers.utils.generic", "transformers.utils.import_utils", "torch._dynamo._trace_wrapped_higher_order_op", "torch.nn", "transformers.cache_utils", "transformers.configuration_utils", "itertools", "typing", "torch", "builtins", "_frozen_importlib", "abc", "functools", "torch._C", "torch._tensor", "torch.nn.attention", "torch.types", "transformers.utils", "transformers.utils.hub"], "hash": "6c5f80a875709090ee27884b6442fe04770afba2", "id": "transformers.masking_utils", "ignore_all": true, "interface_hash": "011540b576e62e370aa1141c2f3ca8bb2c9c12f4", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\masking_utils.py", "plugin_data": null, "size": 57032, "suppressed": [], "version_id": "1.16.1"}