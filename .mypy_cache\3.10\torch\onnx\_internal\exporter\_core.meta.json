{"data_mtime": 1752081394, "dep_lines": [27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 27, 26, 27, 25, 139, 15, 24, 25, 47, 139, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 23, 45, 47, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19, 21, 18], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 10, 20, 5, 10, 20, 25, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 5, 5], "dependencies": ["torch.onnx._internal.exporter._analysis", "torch.onnx._internal.exporter._building", "torch.onnx._internal.exporter._capture_strategies", "torch.onnx._internal.exporter._constants", "torch.onnx._internal.exporter._dispatching", "torch.onnx._internal.exporter._errors", "torch.onnx._internal.exporter._fx_passes", "torch.onnx._internal.exporter._ir_passes", "torch.onnx._internal.exporter._onnx_program", "torch.onnx._internal.exporter._registration", "torch.onnx._internal.exporter._reporting", "torch.onnx._internal.exporter._tensors", "torch.onnx._internal.exporter._verification", "torch.onnx._internal._lazy_import", "torch.onnx._internal.exporter", "torch.export.graph_signature", "torch._subclasses.fake_tensor", "collections.abc", "torch.fx", "torch.export", "numpy.typing", "torch._subclasses", "__future__", "ctypes", "datetime", "inspect", "itertools", "logging", "operator", "pathlib", "textwrap", "traceback", "typing", "torch", "os", "numpy", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._tensor", "torch.export.exported_program", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.node", "torch.jit", "torch.jit._script", "torch.nn", "torch.nn.modules", "torch.nn.modules.module"], "hash": "246b1bc6151d1f556372d87731e4755ffdccc757", "id": "torch.onnx._internal.exporter._core", "ignore_all": true, "interface_hash": "8886b90ab24c666c6dd0fc7d3bbc82721d01efc8", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_core.py", "plugin_data": null, "size": 65223, "suppressed": ["onnxscript.evaluator", "onnxscript.ir", "onnxscript"], "version_id": "1.16.1"}