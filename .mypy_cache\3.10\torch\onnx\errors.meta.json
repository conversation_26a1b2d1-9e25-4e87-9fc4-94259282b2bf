{"data_mtime": 1752081394, "dep_lines": [35, 34, 35, 17, 34, 3, 12, 13, 17, 1, 1, 1], "dep_prios": [20, 20, 20, 25, 20, 5, 10, 5, 25, 5, 30, 30], "dependencies": ["torch.onnx._internal.diagnostics", "torch.onnx._constants", "torch.onnx._internal", "torch._C", "torch.onnx", "__future__", "textwrap", "typing", "torch", "builtins", "_frozen_importlib", "abc"], "hash": "3af7ca8ad04deef5fbb98e6610b7692653a4bdac", "id": "torch.onnx.errors", "ignore_all": true, "interface_hash": "ed5cfe2592df574bef8f833d9b4aee486412a554", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\errors.py", "plugin_data": null, "size": 3837, "suppressed": [], "version_id": "1.16.1"}