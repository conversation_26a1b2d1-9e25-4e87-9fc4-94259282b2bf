{"data_mtime": **********, "dep_lines": [5, 4, 3, 6, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["huggingface_hub.inference._providers._common", "huggingface_hub.inference._common", "huggingface_hub.hf_api", "huggingface_hub.utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "b3cb179ef20bc5f17cbe4f2da44cd91f5ff65416", "id": "huggingface_hub.inference._providers.replicate", "ignore_all": true, "interface_hash": "6046b86765c118796a93aa7cb0cc72f1b8a9022e", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\huggingface_hub\\inference\\_providers\\replicate.py", "plugin_data": null, "size": 3157, "suppressed": [], "version_id": "1.16.1"}