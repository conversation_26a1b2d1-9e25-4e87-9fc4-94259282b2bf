{"data_mtime": 1752081394, "dep_lines": [26, 28, 20, 23, 28, 16, 17, 19, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.superpoint.configuration_superpoint", "transformers.utils.logging", "torch.nn", "transformers.modeling_outputs", "transformers.utils", "dataclasses", "typing", "torch", "transformers", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._tensor", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.container", "torch.nn.modules.conv", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.normalization", "torch.nn.modules.pooling", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modeling_utils", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub"], "hash": "cfb41e33e4d81782279aa02f704b7275d3f6f26d", "id": "transformers.models.superpoint.modeling_superpoint", "ignore_all": true, "interface_hash": "978bf0c1fbd95bd5c4ea8bf2122c8949165e0f72", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\superpoint\\modeling_superpoint.py", "plugin_data": null, "size": 20078, "suppressed": [], "version_id": "1.16.1"}