{"data_mtime": 1752081392, "dep_lines": [27, 24, 25, 26, 27, 19, 21, 23, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.audio_utils", "transformers.feature_extraction_sequence_utils", "transformers.feature_extraction_utils", "transformers.utils", "typing", "numpy", "transformers", "torch", "builtins", "_frozen_importlib", "abc", "collections", "enum", "logging", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "b157f3903343d87fd15be639b7e4a44e36d2e783", "id": "transformers.models.whisper.feature_extraction_whisper", "ignore_all": true, "interface_hash": "5f383b6ea5f42900ec908796426448b0ffe90357", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\whisper\\feature_extraction_whisper.py", "plugin_data": null, "size": 15795, "suppressed": [], "version_id": "1.16.1"}