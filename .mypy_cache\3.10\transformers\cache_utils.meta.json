{"data_mtime": 1752081393, "dep_lines": [15, 2, 5, 10, 12, 14, 15, 1, 2, 3, 4, 6, 7, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19, 971], "dep_prios": [10, 10, 5, 10, 5, 5, 5, 10, 20, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 20], "dependencies": ["transformers.utils.logging", "importlib.metadata", "collections.abc", "packaging.version", "transformers.pytorch_utils", "transformers.configuration_utils", "transformers.utils", "copy", "importlib", "json", "os", "dataclasses", "typing", "torch", "packaging", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "functools", "logging", "torch._C", "torch._C._VariableFunctions", "torch._dynamo", "torch._tensor", "torch.accelerator", "torch.cuda", "torch.cuda.streams", "torch.fx", "torch.fx._pytree", "torch.utils", "torch.utils._pytree", "torch.xpu", "transformers.utils.hub", "transformers.utils.import_utils", "types"], "hash": "44866045c5621fdca6f7f90cb90541a8ecaffbf5", "id": "transformers.cache_utils", "ignore_all": true, "interface_hash": "24b401ddbcc39e7afd5a41862eafc57c99b9dd7a", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\cache_utils.py", "plugin_data": null, "size": 116458, "suppressed": ["hqq.core.quantize", "optimum.quanto"], "version_id": "1.16.1"}