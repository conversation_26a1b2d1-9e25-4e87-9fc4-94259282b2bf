{"data_mtime": 1752081394, "dep_lines": [40, 22, 23, 28, 40, 17, 18, 20, 52, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "math", "typing", "numpy", "torch", "builtins", "PIL", "PIL.Image", "_frozen_importlib", "_typeshed", "abc", "collections", "enum", "logging", "torch._C", "torch._tensor", "transformers.feature_extraction_utils", "transformers.image_processing_base", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "1cbbf0ba29f35e077a70b3c86b6293bd5711d66f", "id": "transformers.models.fuyu.image_processing_fuyu", "ignore_all": true, "interface_hash": "cb5bb56da8f8f545b460fcbdd8d36e46ad8abbc4", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\fuyu\\image_processing_fuyu.py", "plugin_data": null, "size": 33509, "suppressed": [], "version_id": "1.16.1"}