{"data_mtime": 1752081394, "dep_lines": [56, 68, 20, 25, 26, 27, 38, 56, 73, 17, 18, 19, 21, 23, 72, 77, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 81, 82, 81], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20], "dependencies": ["transformers.utils.logging", "transformers.utils.import_utils", "collections.abc", "transformers.feature_extraction_utils", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "torch.nn", "io", "pathlib", "collections", "typing", "numpy", "torch", "PIL", "builtins", "PIL.Image", "_frozen_importlib", "abc", "enum", "functools", "logging", "os", "torch._C", "torch._tensor", "transformers.image_processing_base", "transformers.utils.constants", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "be08d6006798324d1100ffe52f3b2ba960d78550", "id": "transformers.models.conditional_detr.image_processing_conditional_detr", "ignore_all": true, "interface_hash": "0a19fc22d9bb454914c4e9ff0f9d8cb686112262", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\conditional_detr\\image_processing_conditional_detr.py", "plugin_data": null, "size": 85860, "suppressed": ["scipy.special", "scipy.stats", "scipy"], "version_id": "1.16.1"}