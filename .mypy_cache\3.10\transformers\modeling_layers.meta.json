{"data_mtime": 1752081391, "dep_lines": [19, 17, 19, 15, 17, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "torch.nn", "transformers.utils", "functools", "torch", "builtins", "_frozen_importlib", "abc", "logging", "torch.nn.modules", "torch.nn.modules.module", "typing"], "hash": "a3d681c1c4a9967d5aae6b5592bedc4f2678a378", "id": "transformers.modeling_layers", "ignore_all": true, "interface_hash": "d6a89f46c1e6365bde5bceb38ad4fa9e3a78c6c8", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\modeling_layers.py", "plugin_data": null, "size": 3359, "suppressed": [], "version_id": "1.16.1"}