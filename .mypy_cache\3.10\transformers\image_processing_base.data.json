{".class": "MypyFile", "_fullname": "transformers.image_processing_base", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseBatchFeature": {".class": "SymbolTableNode", "cross_ref": "transformers.feature_extraction_utils.BatchFeature", "kind": "Gdef"}, "BatchFeature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.feature_extraction_utils.BatchFeature"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.image_processing_base.BatchFeature", "name": "BatchFeature", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.image_processing_base.BatchFeature", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "transformers.image_processing_base", "mro": ["transformers.image_processing_base.BatchFeature", "transformers.feature_extraction_utils.BatchFeature", "collections.UserDict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.BatchFeature.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.image_processing_base.BatchFeature", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "IMAGE_PROCESSOR_NAME": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.IMAGE_PROCESSOR_NAME", "kind": "Gdef"}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "ImageProcessingMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["transformers.utils.hub.PushToHubMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "transformers.image_processing_base.ImageProcessingMixin", "name": "ImageProcessingMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "transformers.image_processing_base.ImageProcessingMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "transformers.image_processing_base", "mro": ["transformers.image_processing_base.ImageProcessingMixin", "transformers.utils.hub.PushToHubMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.image_processing_base.ImageProcessingMixin.__init__", "name": "__init__", "type": null}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "transformers.image_processing_base.ImageProcessingMixin.__repr__", "name": "__repr__", "type": null}}, "_auto_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_base.ImageProcessingMixin._auto_class", "name": "_auto_class", "setter_type": null, "type": {".class": "NoneType"}}}, "_processor_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "transformers.image_processing_base.ImageProcessingMixin._processor_class", "name": "_processor_class", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_set_processor_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "processor_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin._set_processor_class", "name": "_set_processor_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "processor_class"], "arg_types": ["transformers.image_processing_base.ImageProcessingMixin", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_set_processor_class of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fetch_images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "image_url_or_urls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.fetch_images", "name": "fetch_images", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image_url_or_urls"], "arg_types": ["transformers.image_processing_base.ImageProcessingMixin", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fetch_images of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "from_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "image_processor_dict", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.from_dict", "name": "from_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "image_processor_dict", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.image_processing_base.ImageProcessingMixin"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_dict of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.from_dict", "name": "from_dict", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "image_processor_dict", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.image_processing_base.ImageProcessingMixin"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_dict of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_json_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "json_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.from_json_file", "name": "from_json_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json_file"], "arg_types": [{".class": "TypeType", "item": "transformers.image_processing_base.ImageProcessingMixin"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_json_file of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.from_json_file", "name": "from_json_file", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "json_file"], "arg_types": [{".class": "TypeType", "item": "transformers.image_processing_base.ImageProcessingMixin"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_json_file of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "cache_dir", "force_download", "local_files_only", "token", "revision", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.from_pretrained", "name": "from_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "cache_dir", "force_download", "local_files_only", "token", "revision", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.ImageProcessorType", "id": -1, "name": "ImageProcessorType", "namespace": "transformers.image_processing_base.ImageProcessingMixin.from_pretrained", "upper_bound": "transformers.image_processing_base.ImageProcessingMixin", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_pretrained of ImageProcessingMixin", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.ImageProcessorType", "id": -1, "name": "ImageProcessorType", "namespace": "transformers.image_processing_base.ImageProcessingMixin.from_pretrained", "upper_bound": "transformers.image_processing_base.ImageProcessingMixin", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.ImageProcessorType", "id": -1, "name": "ImageProcessorType", "namespace": "transformers.image_processing_base.ImageProcessingMixin.from_pretrained", "upper_bound": "transformers.image_processing_base.ImageProcessingMixin", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.from_pretrained", "name": "from_pretrained", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "cache_dir", "force_download", "local_files_only", "token", "revision", "kwargs"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.ImageProcessorType", "id": -1, "name": "ImageProcessorType", "namespace": "transformers.image_processing_base.ImageProcessingMixin.from_pretrained", "upper_bound": "transformers.image_processing_base.ImageProcessingMixin", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", "builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "from_pretrained of ImageProcessingMixin", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.ImageProcessorType", "id": -1, "name": "ImageProcessorType", "namespace": "transformers.image_processing_base.ImageProcessingMixin.from_pretrained", "upper_bound": "transformers.image_processing_base.ImageProcessingMixin", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.ImageProcessorType", "id": -1, "name": "ImageProcessorType", "namespace": "transformers.image_processing_base.ImageProcessingMixin.from_pretrained", "upper_bound": "transformers.image_processing_base.ImageProcessingMixin", "values": [], "variance": 0}]}}}}, "get_image_processor_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.get_image_processor_dict", "name": "get_image_processor_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.image_processing_base.ImageProcessingMixin"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_image_processor_dict of ImageProcessingMixin", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.get_image_processor_dict", "name": "get_image_processor_dict", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["cls", "pretrained_model_name_or_path", "kwargs"], "arg_types": [{".class": "TypeType", "item": "transformers.image_processing_base.ImageProcessingMixin"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_image_processor_dict of ImageProcessingMixin", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "register_for_auto_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cls", "auto_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.register_for_auto_class", "name": "register_for_auto_class", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.register_for_auto_class", "name": "register_for_auto_class", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cls", "auto_class"], "arg_types": [{".class": "TypeType", "item": "transformers.image_processing_base.ImageProcessingMixin"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_for_auto_class of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "save_pretrained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "save_directory", "push_to_hub", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.save_pretrained", "name": "save_pretrained", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "save_directory", "push_to_hub", "kwargs"], "arg_types": ["transformers.image_processing_base.ImageProcessingMixin", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "save_pretrained of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.image_processing_base.ImageProcessingMixin"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_dict of ImageProcessingMixin", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_json_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "json_file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.to_json_file", "name": "to_json_file", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "json_file_path"], "arg_types": ["transformers.image_processing_base.ImageProcessingMixin", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_json_file of ImageProcessingMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_json_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "transformers.image_processing_base.ImageProcessingMixin.to_json_string", "name": "to_json_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["transformers.image_processing_base.ImageProcessingMixin"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_json_string of ImageProcessingMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.ImageProcessingMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "transformers.image_processing_base.ImageProcessingMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImageProcessorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "transformers.image_processing_base.ImageProcessorType", "name": "ImageProcessorType", "upper_bound": "transformers.image_processing_base.ImageProcessingMixin", "values": [], "variance": 0}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PushToHubMixin": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.PushToHubMixin", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_base.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_base.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_base.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_base.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_base.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "transformers.image_processing_base.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "cached_file": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.cached_file", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "copy_func": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.doc.copy_func", "kind": "Gdef"}, "custom_object_save": {".class": "SymbolTableNode", "cross_ref": "transformers.dynamic_module_utils.custom_object_save", "kind": "Gdef"}, "download_url": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.download_url", "kind": "Gdef"}, "is_offline_mode": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.is_offline_mode", "kind": "Gdef"}, "is_remote_url": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.hub.is_remote_url", "kind": "Gdef"}, "is_vision_available": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.import_utils.is_vision_available", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "transformers.image_processing_base.logger", "name": "logger", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "transformers.utils.logging", "kind": "Gdef"}, "np": {".class": "SymbolTableNode", "cross_ref": "numpy", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\image_processing_base.py"}