{"data_mtime": 1752081392, "dep_lines": [26, 23, 24, 25, 26, 19, 21, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 31, 31, 31], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 20], "dependencies": ["transformers.utils.logging", "transformers.audio_utils", "transformers.feature_extraction_sequence_utils", "transformers.feature_extraction_utils", "transformers.utils", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "collections", "enum", "logging", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "b89a31406f15c4618bba7a157df7d3b4a65345cd", "id": "transformers.models.speech_to_text.feature_extraction_speech_to_text", "ignore_all": true, "interface_hash": "a0c536c1201e5627405875035d3a7489e1c5b291", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\speech_to_text\\feature_extraction_speech_to_text.py", "plugin_data": null, "size": 13955, "suppressed": ["torchaudio.compliance.kaldi", "torchaudio.compliance", "<PERSON><PERSON><PERSON>"], "version_id": "1.16.1"}