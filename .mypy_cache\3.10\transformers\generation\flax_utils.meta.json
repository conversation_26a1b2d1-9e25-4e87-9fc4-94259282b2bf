{"data_mtime": 1752081394, "dep_lines": [30, 35, 36, 37, 35, 18, 19, 20, 21, 22, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 24, 25], "dep_prios": [5, 10, 5, 5, 5, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 5], "dependencies": ["transformers.models.auto", "transformers.utils.logging", "transformers.generation.configuration_utils", "transformers.generation.flax_logits_process", "transformers.utils", "copy", "inspect", "warnings", "functools", "typing", "numpy", "builtins", "_frozen_importlib", "_typeshed", "_warnings", "abc", "collections", "logging", "transformers.configuration_utils", "transformers.utils.generic", "transformers.utils.hub", "types"], "hash": "63cbb744481a6fd20317ef5cc84392c0cb5b54ec", "id": "transformers.generation.flax_utils", "ignore_all": true, "interface_hash": "007aacf16d865994281246f7a80ebfbd8d1b0fe2", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\generation\\flax_utils.py", "plugin_data": null, "size": 50641, "suppressed": ["jax.numpy", "flax", "jax"], "version_id": "1.16.1"}