{"data_mtime": 1752081394, "dep_lines": [21, 19, 20, 19, 17, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.vilt.image_processing_vilt", "transformers.utils.logging", "transformers.utils.import_utils", "transformers.utils", "warnings", "builtins", "_frozen_importlib", "abc", "logging", "transformers.image_processing_base", "transformers.image_processing_utils", "transformers.utils.hub", "typing"], "hash": "50d60d9bd895716f9a2560573db896ce5d63d1c4", "id": "transformers.models.vilt.feature_extraction_vilt", "ignore_all": true, "interface_hash": "bca0daf44a6f3d4189c59abc61e180bfbfc78e58", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\vilt\\feature_extraction_vilt.py", "plugin_data": null, "size": 1284, "suppressed": [], "version_id": "1.16.1"}