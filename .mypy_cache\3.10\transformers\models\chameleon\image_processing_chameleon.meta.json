{"data_mtime": 1752081394, "dep_lines": [34, 21, 22, 23, 34, 17, 19, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "typing", "numpy", "PIL", "builtins", "PIL.Image", "_frozen_importlib", "abc", "enum", "functools", "logging", "torch", "torch._C", "torch._tensor", "transformers.image_processing_base", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "813ba0173592143461725db3ae1cc723ef0ada8b", "id": "transformers.models.chameleon.image_processing_chameleon", "ignore_all": true, "interface_hash": "7d2b8507eae28638fbc7a403d8a0cd15e037d9ce", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\chameleon\\image_processing_chameleon.py", "plugin_data": null, "size": 16884, "suppressed": [], "version_id": "1.16.1"}