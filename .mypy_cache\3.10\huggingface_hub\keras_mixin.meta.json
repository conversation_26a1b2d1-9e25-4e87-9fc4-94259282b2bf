{"data_mtime": 1752081392, "dep_lines": [21, 22, 1, 11, 19, 20, 1, 2, 3, 4, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 36, 34], "dep_prios": [10, 5, 10, 5, 10, 5, 20, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10], "dependencies": ["huggingface_hub.utils.logging", "huggingface_hub.utils._typing", "collections.abc", "huggingface_hub.utils", "huggingface_hub.constants", "huggingface_hub.hf_api", "collections", "json", "os", "warnings", "functools", "pathlib", "shutil", "typing", "huggingface_hub", "builtins", "_frozen_importlib", "abc", "genericpath", "huggingface_hub._snapshot_download", "huggingface_hub.hub_mixin", "huggingface_hub.utils._runtime", "huggingface_hub.utils._validators", "logging"], "hash": "31f996974ab6f0cf380002d24161f930d950591e", "id": "huggingface_hub.keras_mixin", "ignore_all": true, "interface_hash": "af72a398834f5601b955b730fb55140fd74a4cc9", "mtime": 1752005469, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\huggingface_hub\\keras_mixin.py", "plugin_data": null, "size": 19574, "suppressed": ["tensorflow", "tf_keras"], "version_id": "1.16.1"}