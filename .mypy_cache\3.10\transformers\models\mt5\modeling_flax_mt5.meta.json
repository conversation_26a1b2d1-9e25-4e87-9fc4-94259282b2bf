{"data_mtime": 1752081394, "dep_lines": [20, 21, 19, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17, 17], "dep_prios": [5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20], "dependencies": ["transformers.models.t5.modeling_flax_t5", "transformers.models.mt5.configuration_mt5", "transformers.utils.logging", "transformers.utils", "builtins", "_frozen_importlib", "abc", "logging", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.modeling_flax_utils", "transformers.models.t5", "transformers.utils.hub", "typing"], "hash": "21fb718268008129b3d53573c188e4a47567c5cd", "id": "transformers.models.mt5.modeling_flax_mt5", "ignore_all": true, "interface_hash": "cccd256e8ca755db222d583d2a2215c4b6114547", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\mt5\\modeling_flax_mt5.py", "plugin_data": null, "size": 4329, "suppressed": ["jax.numpy", "jax"], "version_id": "1.16.1"}