{"data_mtime": 1752081394, "dep_lines": [137, 17, 18, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.configuration_utils", "transformers.utils", "builtins", "_frozen_importlib", "abc", "transformers.models.auto", "transformers.utils.doc", "transformers.utils.hub", "typing"], "hash": "23c2325a22b65ede377244984bcb7e3112bc7bd0", "id": "transformers.models.rag.configuration_rag", "ignore_all": true, "interface_hash": "0251ea630d5e0e17ea6ed64a9d1baa1753d42465", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\rag\\configuration_rag.py", "plugin_data": null, "size": 8523, "suppressed": [], "version_id": "1.16.1"}