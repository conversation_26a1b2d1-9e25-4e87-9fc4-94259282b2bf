{"data_mtime": 1752081394, "dep_lines": [21, 22, 15, 16, 17, 19, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.cache_utils", "transformers.utils", "warnings", "dataclasses", "typing", "torch", "builtins", "_frozen_importlib", "abc", "collections", "torch._C", "torch._tensor", "transformers.utils.generic"], "hash": "db4bbd57cb539b16165c4c334eecab63c0bf5397", "id": "transformers.modeling_outputs", "ignore_all": true, "interface_hash": "46dd19d2dba56d2c9baefc9b8f0b49f3b7e0bbe2", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\modeling_outputs.py", "plugin_data": null, "size": 109642, "suppressed": [], "version_id": "1.16.1"}