{"data_mtime": 1752081394, "dep_lines": [14, 14, 14, 14, 12, 13, 22, 29, 4, 6, 7, 8, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, 24, 774], "dep_prios": [10, 10, 10, 20, 10, 10, 25, 25, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25, 20], "dependencies": ["torch.onnx._internal.fx.diagnostics", "torch.onnx._internal.fx.registration", "torch.onnx._internal.fx.type_utils", "torch.onnx._internal.fx", "torch._ops", "torch.fx", "collections.abc", "torch.onnx", "__future__", "logging", "operator", "types", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "functools", "torch._C", "torch._tensor", "torch.fx.node", "torch.onnx._internal._exporter_legacy", "torch.onnx._internal.diagnostics", "torch.onnx._internal.diagnostics._rules", "torch.onnx._internal.diagnostics.infra", "torch.onnx._internal.diagnostics.infra._infra", "torch.onnx._internal.diagnostics.infra.context"], "hash": "075931188c2a48eae504e9f8cf12ed81a7537952", "id": "torch.onnx._internal.fx.onnxfunction_dispatcher", "ignore_all": true, "interface_hash": "3f5e784ba5faa5084cecea2df7adc83058f1a6af", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\fx\\onnxfunction_dispatcher.py", "plugin_data": null, "size": 38674, "suppressed": ["onnxscript.function_libs.torch_lib", "onnxscript", "onnx"], "version_id": "1.16.1"}