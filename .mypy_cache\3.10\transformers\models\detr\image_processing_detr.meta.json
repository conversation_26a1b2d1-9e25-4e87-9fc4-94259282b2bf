{"data_mtime": 1752081394, "dep_lines": [55, 67, 20, 25, 26, 37, 55, 72, 17, 18, 19, 21, 23, 71, 76, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 80, 81, 80], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20], "dependencies": ["transformers.utils.logging", "transformers.utils.import_utils", "collections.abc", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "torch.nn", "io", "pathlib", "collections", "typing", "numpy", "torch", "PIL", "builtins", "PIL.Image", "_frozen_importlib", "abc", "enum", "functools", "logging", "os", "torch._C", "torch._tensor", "transformers.feature_extraction_utils", "transformers.image_processing_base", "transformers.utils.constants", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "c0050caa0dacb7260b8028c43c1fa582b9d86941", "id": "transformers.models.detr.image_processing_detr", "ignore_all": true, "interface_hash": "dd82a415ac9f696d60a96618c8fa16035a64acc0", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\detr\\image_processing_detr.py", "plugin_data": null, "size": 94108, "suppressed": ["scipy.special", "scipy.stats", "scipy"], "version_id": "1.16.1"}