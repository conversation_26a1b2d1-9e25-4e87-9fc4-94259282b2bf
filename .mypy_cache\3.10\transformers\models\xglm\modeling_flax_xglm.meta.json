{"data_mtime": 1752081393, "dep_lines": [39, 38, 33, 37, 38, 17, 18, 19, 20, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 28, 22, 24, 29, 31, 22, 23], "dep_prios": [5, 10, 5, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 10, 5, 5, 20, 5], "dependencies": ["transformers.models.xglm.configuration_xglm", "transformers.utils.logging", "transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "math", "random", "functools", "typing", "numpy", "builtins", "_frozen_importlib", "abc", "collections", "logging", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "3b4f4477ded239abb86b60edc74184c07dbe7276", "id": "transformers.models.xglm.modeling_flax_xglm", "ignore_all": true, "interface_hash": "f428b118ca0033eb712a73e5f333e716fc463a9c", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\xglm\\modeling_flax_xglm.py", "plugin_data": null, "size": 33217, "suppressed": ["flax.core.frozen_dict", "flax.linen.attention", "flax.linen", "jax.numpy", "flax.traverse_util", "jax.random", "flax", "jax"], "version_id": "1.16.1"}