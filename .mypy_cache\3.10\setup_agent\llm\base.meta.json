{"data_mtime": 1752081338, "dep_lines": [5, 6, 7, 1, 1], "dep_prios": [5, 5, 5, 5, 30], "dependencies": ["abc", "typing", "dataclasses", "builtins", "_frozen_importlib"], "hash": "c62859be435d67233a10b251478e91014e3cb309", "id": "setup_agent.llm.base", "ignore_all": false, "interface_hash": "2f0f5cecc126e57be74e3c335c7e99b91f9897d4", "mtime": 1749282081, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\llm\\base.py", "plugin_data": null, "size": 2344, "suppressed": [], "version_id": "1.16.1"}