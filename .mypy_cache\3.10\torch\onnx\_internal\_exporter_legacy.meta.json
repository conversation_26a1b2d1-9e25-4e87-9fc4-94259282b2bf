{"data_mtime": 1752081394, "dep_lines": [32, 33, 33, 34, 34, 34, 52, 321, 367, 556, 570, 584, 30, 31, 32, 33, 34, 28, 29, 30, 51, 564, 27, 28, 29, 46, 51, 2, 17, 18, 19, 20, 21, 22, 23, 24, 26, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 48, 49], "dep_prios": [10, 10, 10, 10, 10, 10, 20, 20, 20, 20, 20, 20, 10, 5, 20, 20, 20, 10, 10, 20, 25, 20, 10, 20, 20, 25, 25, 5, 10, 10, 10, 10, 10, 5, 5, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 25], "dependencies": ["torch.onnx._internal.diagnostics.infra", "torch.onnx._internal.exporter._constants", "torch.onnx._internal.exporter._onnx_program", "torch.onnx._internal.fx.decomposition_table", "torch.onnx._internal.fx.patcher", "torch.onnx._internal.fx.registration", "torch.onnx._internal.fx.diagnostics", "torch.onnx._internal.fx.dynamo_graph_extractor", "torch.onnx._internal.fx.onnxfunction_dispatcher", "torch.onnx._internal.fx.fx_symbolic_graph_extractor", "torch.onnx._internal.fx.decomposition_skip", "torch.onnx._internal.fx.fx_onnx_interpreter", "torch.onnx._internal.io_adapter", "torch.onnx._internal._lazy_import", "torch.onnx._internal.diagnostics", "torch.onnx._internal.exporter", "torch.onnx._internal.fx", "torch.utils._pytree", "torch.onnx.errors", "torch.onnx._internal", "torch._subclasses.fake_tensor", "torch.export._trace", "torch._ops", "torch.utils", "torch.onnx", "collections.abc", "torch._subclasses", "__future__", "abc", "contextlib", "dataclasses", "logging", "warnings", "collections", "typing", "typing_extensions", "torch", "io", "builtins", "_frozen_importlib", "_io", "_typeshed", "_warnings", "torch._C", "torch._dynamo", "torch._dynamo.config", "torch._tensor", "torch.export", "torch.export.exported_program", "torch.fx", "torch.fx.graph_module", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.onnx._internal.diagnostics.infra._infra", "torch.onnx._internal.diagnostics.infra.context", "torch.utils._config_typing", "torch.utils._python_dispatch", "types"], "hash": "4da9a029e3e9e6e89af3cd02ac9fa5c2b408f55e", "id": "torch.onnx._internal._exporter_legacy", "ignore_all": true, "interface_hash": "39e82c2f71d126b7d36566769c0581b6e0bd6089", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\_exporter_legacy.py", "plugin_data": null, "size": 36081, "suppressed": ["onnxruntime", "onnxscript"], "version_id": "1.16.1"}