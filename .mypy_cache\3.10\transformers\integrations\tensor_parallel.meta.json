{"data_mtime": 1752081391, "dep_lines": [26, 27, 37, 23, 24, 26, 14, 16, 17, 18, 19, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 10, 10, 5, 5, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.utils.generic", "torch.distributed.tensor", "torch.distributed", "torch.nn", "transformers.utils", "__future__", "operator", "os", "re", "functools", "typing", "torch", "builtins", "_frozen_importlib", "abc", "logging", "torch._C", "torch._tensor", "torch.distributed.tensor._api", "torch.distributed.tensor._dtensor_spec", "torch.distributed.tensor.placement_types", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.sparse", "torch.nn.parameter", "transformers.utils.import_utils", "types"], "hash": "24d8cd3078814273e2da0f4b8ac3fe952a6cfcf3", "id": "transformers.integrations.tensor_parallel", "ignore_all": true, "interface_hash": "4f9c06a7c23083904407fd186cce63cc4921e94d", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\integrations\\tensor_parallel.py", "plugin_data": null, "size": 40143, "suppressed": [], "version_id": "1.16.1"}