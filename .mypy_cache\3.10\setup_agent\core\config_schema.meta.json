{"data_mtime": 1752082281, "dep_lines": [8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["typing", "re", "pathlib", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "os", "types", "typing_extensions"], "hash": "0b68dfd9826db3987fd5dce8ae71f43593bf2bd0", "id": "setup_agent.core.config_schema", "ignore_all": false, "interface_hash": "424ea969ea7ab215a92a234a0c0e33c2ce8969a3", "mtime": 1752082248, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\core\\config_schema.py", "plugin_data": null, "size": 12727, "suppressed": [], "version_id": "1.16.1"}