{"data_mtime": 1752082282, "dep_lines": [12, 13, 5, 6, 7, 8, 9, 10, 155, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["setup_agent.core.config", "setup_agent.core.exceptions", "os", "shutil", "logging", "pathlib", "typing", "contextlib", "tempfile", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "setup_agent.core", "types", "typing_extensions"], "hash": "d678461797a8841d39a4480f73e661de58cb353a", "id": "setup_agent.utils.file_utils", "ignore_all": false, "interface_hash": "9be9620d36dd9582c84f2210648c6bb7c99da9e1", "mtime": 1749333037, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\utils\\file_utils.py", "plugin_data": null, "size": 6660, "suppressed": [], "version_id": "1.16.1"}