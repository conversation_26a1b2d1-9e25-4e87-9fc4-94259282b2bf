{"data_mtime": 1752081390, "dep_lines": [19, 22, 14, 15, 16, 17, 19, 21, 27, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 5, 20, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["packaging.version", "transformers.utils", "inspect", "warnings", "functools", "typing", "packaging", "transformers", "torch", "builtins", "_frozen_importlib", "abc", "enum", "transformers.utils.generic", "transformers.utils.import_utils"], "hash": "93bd5f6aa68fa61dabb71fcc3c59271733641e02", "id": "transformers.utils.deprecation", "ignore_all": true, "interface_hash": "aa8bc1470fb7d17d559c7d01ced0da92e821f06e", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\utils\\deprecation.py", "plugin_data": null, "size": 8065, "suppressed": [], "version_id": "1.16.1"}