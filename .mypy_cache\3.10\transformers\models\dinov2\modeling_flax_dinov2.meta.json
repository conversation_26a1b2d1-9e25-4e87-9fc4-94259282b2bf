{"data_mtime": 1752081394, "dep_lines": [36, 17, 28, 29, 35, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24, 25, 21, 23, 26, 21, 22], "dep_prios": [5, 10, 5, 5, 5, 20, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10, 10, 5, 20, 10], "dependencies": ["transformers.models.dinov2.configuration_dinov2", "collections.abc", "transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "collections", "math", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.utils.backbone_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "16979778f8fa7fe8a81d9ca908da12e7ca68050c", "id": "transformers.models.dinov2.modeling_flax_dinov2", "ignore_all": true, "interface_hash": "2914365af684033b3d0555d532cc2f6bbb1645d1", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\dinov2\\modeling_flax_dinov2.py", "plugin_data": null, "size": 31050, "suppressed": ["flax.core.frozen_dict", "flax.linen.attention", "flax.linen", "jax.numpy", "flax.traverse_util", "flax", "jax"], "version_id": "1.16.1"}