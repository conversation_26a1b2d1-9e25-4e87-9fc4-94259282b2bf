{"data_mtime": 1752081394, "dep_lines": [37, 37, 32, 36, 36, 36, 36, 37, 36, 29, 31, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 10, 20, 20, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.jit_utils", "torch.onnx._internal.registration", "torch.nn.functional", "torch.onnx._type_utils", "torch.onnx.errors", "torch.onnx.symbolic_helper", "torch.onnx.utils", "torch.onnx._internal", "torch.onnx", "functools", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "typing"], "hash": "28d3829c58b4bd48394cf7cfd5f1c98fba007369", "id": "torch.onnx.symbolic_opset16", "ignore_all": true, "interface_hash": "44f8b179462975b78a9ab9043a5dd6056b4e4e22", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\symbolic_opset16.py", "plugin_data": null, "size": 6595, "suppressed": [], "version_id": "1.16.1"}