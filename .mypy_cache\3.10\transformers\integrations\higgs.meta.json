{"data_mtime": 1752081391, "dep_lines": [18, 27, 16, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 31, 32, 35], "dep_prios": [5, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["transformers.utils", "torch.nn", "math", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "transformers.utils.import_utils", "types", "typing"], "hash": "d67a306a0747df42c2d9d5a97852057e7c3c1504", "id": "transformers.integrations.higgs", "ignore_all": true, "interface_hash": "faab07555312fc7b8d65fb55be3b4701ec2e3533", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\integrations\\higgs.py", "plugin_data": null, "size": 31400, "suppressed": ["flute.integrations.higgs", "flute.tune", "fast_hadamard_transform"], "version_id": "1.16.1"}