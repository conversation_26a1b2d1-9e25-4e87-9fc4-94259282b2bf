{"data_mtime": 1752081394, "dep_lines": [27, 38, 66, 26, 27, 42, 53, 17, 18, 19, 20, 21, 22, 23, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.utils.deprecation", "transformers.generation.logits_process", "transformers.configuration_utils", "transformers.utils", "transformers.modeling_utils", "transformers.cache_utils", "copy", "json", "os", "warnings", "abc", "dataclasses", "typing", "transformers", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "enum", "genericpath", "json.decoder", "logging", "ntpath", "torch", "torch._C", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "db5dd93a3a4ff584650c262c1ac27048ee11f3d9", "id": "transformers.generation.configuration_utils", "ignore_all": true, "interface_hash": "e49873136420ed3669ac8ef95ef75d1b1a7047a1", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\generation\\configuration_utils.py", "plugin_data": null, "size": 85405, "suppressed": [], "version_id": "1.16.1"}