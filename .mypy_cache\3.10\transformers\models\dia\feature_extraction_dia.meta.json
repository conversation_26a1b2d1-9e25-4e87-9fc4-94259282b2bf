{"data_mtime": 1752081393, "dep_lines": [23, 21, 22, 23, 17, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.feature_extraction_sequence_utils", "transformers.feature_extraction_utils", "transformers.utils", "typing", "numpy", "builtins", "_ctypes", "_frozen_importlib", "abc", "array", "collections", "ctypes", "datetime", "decimal", "enum", "fractions", "logging", "mmap", "numbers", "numpy._core", "numpy._core.fromnumeric", "numpy._core.multiarray", "numpy._globals", "numpy._typing", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "numpy.dtypes", "transformers.utils.generic", "transformers.utils.hub", "types", "uuid"], "hash": "3810f7f158eff39afe501cbd932092942b74fadb", "id": "transformers.models.dia.feature_extraction_dia", "ignore_all": true, "interface_hash": "5caf79236ba5147d823548c2a5b0f5177bf9944d", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\dia\\feature_extraction_dia.py", "plugin_data": null, "size": 8503, "suppressed": [], "version_id": "1.16.1"}