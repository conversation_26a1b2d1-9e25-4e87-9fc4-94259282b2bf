{"data_mtime": 1752081391, "dep_lines": [16, 15, 16, 21, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 27, 24, 27], "dep_prios": [10, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 5, 20], "dependencies": ["transformers.utils.logging", "transformers.activations", "transformers.utils", "torch.nn", "torch", "builtins", "_frozen_importlib", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.parameter", "transformers.utils.import_utils", "types", "typing"], "hash": "597f87a5d75ab3089613d97740ea681c2556027a", "id": "transformers.integrations.fbgemm_fp8", "ignore_all": true, "interface_hash": "894a6de9d77be11ad70dcf3a64d806856f0005c8", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\integrations\\fbgemm_fp8.py", "plugin_data": null, "size": 12441, "suppressed": ["fbgemm_gpu.experimental.gen_ai", "fbgemm_gpu.experimental", "accelerate", "fbgemm_gpu"], "version_id": "1.16.1"}