{"data_mtime": 1752081393, "dep_lines": [38, 57, 58, 1234, 34, 35, 36, 37, 38, 63, 1234, 17, 18, 19, 20, 21, 22, 23, 62, 1234, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 29, 25, 27, 28, 30, 31, 32, 25, 26, 28], "dep_prios": [10, 5, 5, 20, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 10, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10, 10, 5, 5, 5, 20, 10, 20], "dependencies": ["transformers.utils.logging", "transformers.utils.hub", "transformers.utils.import_utils", "transformers.models.auto", "transformers.configuration_utils", "transformers.dynamic_module_utils", "transformers.generation", "transformers.modeling_flax_pytorch_utils", "transformers.utils", "safetensors.flax", "transformers.models", "gc", "json", "os", "warnings", "functools", "pickle", "typing", "safetensors", "transformers", "builtins", "_frozen_importlib", "_io", "_typeshed", "_warnings", "abc", "genericpath", "io", "json.encoder", "logging", "ntpath", "pathlib", "posixpath", "transformers.generation.configuration_utils", "transformers.generation.flax_utils", "transformers.utils.doc", "types", "typing_extensions"], "hash": "5550d5d2042c2e63ba6776a7fb666726b0222dc7", "id": "transformers.modeling_flax_utils", "ignore_all": true, "interface_hash": "a82d1a1458a395cec322a5355290dd1a311c60c5", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\modeling_flax_utils.py", "plugin_data": null, "size": 61341, "suppressed": ["flax.core.frozen_dict", "flax.linen", "jax.numpy", "msgpack.exceptions", "flax.serialization", "flax.traverse_util", "jax.random", "flax", "jax", "msgpack"], "version_id": "1.16.1"}