{"data_mtime": 1752081391, "dep_lines": [21, 23, 141, 21, 23, 15, 16, 17, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 104, 102, 100, 117], "dep_prios": [10, 10, 5, 20, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["torch.nn.functional", "transformers.utils.logging", "transformers.integrations.npu_flash_attention", "torch.nn", "transformers.utils", "inspect", "os", "warnings", "typing", "torch", "builtins", "_frozen_importlib", "abc", "functools", "logging", "torch._C", "torch._tensor", "transformers.integrations", "transformers.utils.import_utils", "types"], "hash": "252470da69bb4b8d9be71e1e231555358f4a8384", "id": "transformers.modeling_flash_attention_utils", "ignore_all": true, "interface_hash": "377fedcecbd0c773c76342fcfa75fc0b5ec84514", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\modeling_flash_attention_utils.py", "plugin_data": null, "size": 25624, "suppressed": ["flash_attn.layers.rotary", "flash_attn.bert_padding", "flash_attn", "flash_attn_interface"], "version_id": "1.16.1"}