{"data_mtime": 1752081394, "dep_lines": [39, 28, 635, 23, 26, 27, 28, 43, 17, 18, 19, 20, 21, 22, 24, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 20, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.utils.logging", "transformers.models.timm_backbone", "collections.abc", "transformers.configuration_utils", "transformers.dynamic_module_utils", "transformers.utils", "transformers.generation", "copy", "importlib", "json", "os", "warnings", "collections", "typing", "builtins", "_frozen_importlib", "abc", "logging", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "56c2f46f9b1b7a08120daa2e313ffa63e36cfcbb", "id": "transformers.models.auto.auto_factory", "ignore_all": true, "interface_hash": "b01847f3275f7b6e487e05eb2a1dc37595584050", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\auto\\auto_factory.py", "plugin_data": null, "size": 47135, "suppressed": [], "version_id": "1.16.1"}