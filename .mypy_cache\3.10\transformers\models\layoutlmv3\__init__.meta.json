{"data_mtime": 1752081391, "dep_lines": [21, 22, 23, 24, 25, 26, 27, 28, 29, 17, 16, 14, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 5, 30, 30], "dependencies": ["transformers.models.layoutlmv3.configuration_layoutlmv3", "transformers.models.layoutlmv3.feature_extraction_layoutlmv3", "transformers.models.layoutlmv3.image_processing_layoutlmv3", "transformers.models.layoutlmv3.image_processing_layoutlmv3_fast", "transformers.models.layoutlmv3.modeling_layoutlmv3", "transformers.models.layoutlmv3.modeling_tf_layoutlmv3", "transformers.models.layoutlmv3.processing_layoutlmv3", "transformers.models.layoutlmv3.tokenization_layoutlmv3", "transformers.models.layoutlmv3.tokenization_layoutlmv3_fast", "transformers.utils.import_utils", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "943b2f2515a1040c84f8e3a7b65c1fdaddb40e3d", "id": "transformers.models.layoutlmv3", "ignore_all": true, "interface_hash": "8a96fac27333f35ce25f939b55a3d430c7923106", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\layoutlmv3\\__init__.py", "plugin_data": null, "size": 1323, "suppressed": [], "version_id": "1.16.1"}