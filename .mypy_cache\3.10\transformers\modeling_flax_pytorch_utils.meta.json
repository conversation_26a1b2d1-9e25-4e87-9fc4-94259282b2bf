{"data_mtime": 1752081390, "dep_lines": [29, 29, 37, 17, 18, 22, 26, 33, 36, 1, 1, 1, 1, 1, 1, 21, 23, 24, 20], "dep_prios": [10, 5, 5, 10, 5, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 10, 5, 5, 10], "dependencies": ["transformers.utils.logging", "transformers.utils", "safetensors.flax", "os", "pickle", "numpy", "transformers", "torch", "safetensors", "builtins", "_frozen_importlib", "abc", "logging", "transformers.utils.import_utils", "typing"], "hash": "6e82a77a101d0bb29a1ec3c0bf0ba8f0ea4f1f97", "id": "transformers.modeling_flax_pytorch_utils", "ignore_all": true, "interface_hash": "70a0c91425558a054bf533db74f66e8914199438", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\modeling_flax_pytorch_utils.py", "plugin_data": null, "size": 21592, "suppressed": ["jax.numpy", "flax.serialization", "flax.traverse_util", "jax"], "version_id": "1.16.1"}