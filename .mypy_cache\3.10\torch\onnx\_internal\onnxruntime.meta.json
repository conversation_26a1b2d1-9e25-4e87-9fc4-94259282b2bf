{"data_mtime": 1752081394, "dep_lines": [32, 33, 589, 881, 1078, 17, 18, 19, 30, 31, 32, 1135, 13, 15, 16, 20, 29, 6, 11, 12, 13, 14, 20, 28, 2, 3, 4, 5, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 1134, 24, 25], "dep_prios": [20, 20, 20, 20, 20, 5, 5, 5, 20, 25, 20, 20, 10, 5, 5, 10, 20, 5, 10, 10, 20, 10, 20, 20, 10, 10, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 25, 20], "dependencies": ["torch.onnx._internal.fx.decomposition_table", "torch.onnx._internal.fx.passes", "torch.onnx._internal.fx.type_utils", "torch.onnx._internal.fx.fx_onnx_interpreter", "torch.fx.passes.infra.partitioner", "torch.fx.passes.fake_tensor_prop", "torch.fx.passes.operator_support", "torch.fx.passes.tools_common", "torch.onnx._internal._exporter_legacy", "torch.onnx._internal.diagnostics", "torch.onnx._internal.fx", "torch._dynamo.backends.common", "torch._prims.executor", "torch._subclasses.fake_tensor", "torch.fx._compatibility", "torch.utils._pytree", "torch.onnx._internal", "collections.abc", "torch._C", "torch._ops", "torch._prims", "torch.fx", "torch.utils", "torch.onnx", "dataclasses", "importlib", "logging", "os", "typing", "typing_extensions", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "torch._subclasses", "torch._tensor", "torch.fx.graph", "torch.fx.graph_module", "torch.fx.interpreter", "torch.fx.node", "torch.fx.passes", "torch.fx.passes.infra", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.onnx._internal.diagnostics.infra", "torch.onnx._internal.diagnostics.infra._infra", "torch.onnx._internal.diagnostics.infra.context", "torch.onnx._internal.fx._pass", "torch.onnx._internal.fx.diagnostics", "torch.onnx._internal.fx.onnxfunction_dispatcher", "torch.onnx._internal.fx.passes.type_promotion", "torch.onnx._internal.fx.passes.virtualization", "torch.testing", "torch.testing._comparison", "torch.types", "torch.utils._python_dispatch", "types"], "hash": "66f4583a7c67ca12e836cbb3ebd719b2f3793b6e", "id": "torch.onnx._internal.onnxruntime", "ignore_all": true, "interface_hash": "de0e347ba3d4963603648008b926c12291913ab9", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\onnxruntime.py", "plugin_data": null, "size": 53345, "suppressed": ["onnxruntime.capi", "functorch.compile", "onnx", "onnxruntime"], "version_id": "1.16.1"}