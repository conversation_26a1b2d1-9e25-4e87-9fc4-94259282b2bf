{"data_mtime": 1752081391, "dep_lines": [24, 23, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 19, 20, 21, 18], "dep_prios": [5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 5, 10], "dependencies": ["transformers.utils.logging", "transformers.utils", "inspect", "builtins", "_frozen_importlib", "_typeshed", "abc", "logging", "transformers.utils.doc", "types", "typing", "typing_extensions"], "hash": "cba4b864837c7ee5be62b9d7b42a12215f210948", "id": "transformers.generation.flax_logits_process", "ignore_all": true, "interface_hash": "c39f83eb87f6ea4981df67a2d6a23d64c0f4b944", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\generation\\flax_logits_process.py", "plugin_data": null, "size": 23008, "suppressed": ["jax.lax", "jax.numpy", "jax.experimental", "jax"], "version_id": "1.16.1"}