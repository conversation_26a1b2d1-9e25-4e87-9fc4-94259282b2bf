{"data_mtime": 1752081391, "dep_lines": [17, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 16, 14, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 20, 20, 20, 30, 30], "dependencies": ["transformers.utils.import_utils", "transformers.models.albert", "transformers.models.align", "transformers.models.altclip", "transformers.models.arcee", "transformers.models.aria", "transformers.models.audio_spectrogram_transformer", "transformers.models.auto", "transformers.models.autoformer", "transformers.models.aya_vision", "transformers.models.bamba", "transformers.models.bark", "transformers.models.bart", "transformers.models.barthez", "transformers.models.bartpho", "transformers.models.beit", "transformers.models.bert", "transformers.models.bert_generation", "transformers.models.bert_japanese", "transformers.models.bertweet", "transformers.models.big_bird", "transformers.models.bigbird_pegasus", "transformers.models.biogpt", "transformers.models.bit", "transformers.models.bitnet", "transformers.models.blenderbot", "transformers.models.blenderbot_small", "transformers.models.blip", "transformers.models.blip_2", "transformers.models.bloom", "transformers.models.bridgetower", "transformers.models.bros", "transformers.models.byt5", "transformers.models.camembert", "transformers.models.canine", "transformers.models.chameleon", "transformers.models.chinese_clip", "transformers.models.clap", "transformers.models.clip", "transformers.models.clipseg", "transformers.models.clvp", "transformers.models.code_llama", "transformers.models.codegen", "transformers.models.cohere", "transformers.models.cohere2", "transformers.models.colpali", "transformers.models.colqwen2", "transformers.models.conditional_detr", "transformers.models.convbert", "transformers.models.convnext", "transformers.models.convnextv2", "transformers.models.cpm", "transformers.models.cpmant", "transformers.models.csm", "transformers.models.ctrl", "transformers.models.cvt", "transformers.models.d_fine", "transformers.models.dab_detr", "transformers.models.dac", "transformers.models.data2vec", "transformers.models.dbrx", "transformers.models.deberta", "transformers.models.deberta_v2", "transformers.models.decision_transformer", "transformers.models.deepseek_v3", "transformers.models.deformable_detr", "transformers.models.deit", "transformers.models.deprecated", "transformers.models.depth_anything", "transformers.models.depth_pro", "transformers.models.detr", "transformers.models.dia", "transformers.models.dialogpt", "transformers.models.diffllama", "transformers.models.dinat", "transformers.models.dinov2", "transformers.models.dinov2_with_registers", "transformers.models.distilbert", "transformers.models.dit", "transformers.models.donut", "transformers.models.dots1", "transformers.models.dpr", "transformers.models.dpt", "transformers.models.efficientnet", "transformers.models.electra", "transformers.models.emu3", "transformers.models.encodec", "transformers.models.encoder_decoder", "transformers.models.ernie", "transformers.models.esm", "transformers.models.falcon", "transformers.models.falcon_h1", "transformers.models.falcon_mamba", "transformers.models.fastspeech2_conformer", "transformers.models.flaubert", "transformers.models.flava", "transformers.models.fnet", "transformers.models.focalnet", "transformers.models.fsmt", "transformers.models.funnel", "transformers.models.fuyu", "transformers.models.gemma", "transformers.models.gemma2", "transformers.models.gemma3", "transformers.models.git", "transformers.models.glm", "transformers.models.glm4", "transformers.models.glpn", "transformers.models.got_ocr2", "transformers.models.gpt2", "transformers.models.gpt_bigcode", "transformers.models.gpt_neo", "transformers.models.gpt_neox", "transformers.models.gpt_neox_japanese", "transformers.models.gpt_sw3", "transformers.models.gptj", "transformers.models.granite", "transformers.models.granite_speech", "transformers.models.granitemoe", "transformers.models.granitemoehybrid", "transformers.models.granitemoeshared", "transformers.models.grounding_dino", "transformers.models.groupvit", "transformers.models.helium", "transformers.models.herbert", "transformers.models.hgnet_v2", "transformers.models.hiera", "transformers.models.hubert", "transformers.models.ibert", "transformers.models.idefics", "transformers.models.idefics2", "transformers.models.idefics3", "transformers.models.ijepa", "transformers.models.imagegpt", "transformers.models.informer", "transformers.models.instructblip", "transformers.models.instructblipvideo", "transformers.models.internvl", "transformers.models.jamba", "transformers.models.janus", "transformers.models.jetmoe", "transformers.models.kosmos2", "transformers.models.kyutai_speech_to_text", "transformers.models.layoutlm", "transformers.models.layoutlmv2", "transformers.models.layoutlmv3", "transformers.models.layoutxlm", "transformers.models.led", "transformers.models.levit", "transformers.models.lightglue", "transformers.models.lilt", "transformers.models.llama", "transformers.models.llama4", "transformers.models.llava", "transformers.models.llava_next", "transformers.models.llava_next_video", "transformers.models.llava_onevision", "transformers.models.longformer", "transformers.models.longt5", "transformers.models.luke", "transformers.models.lxmert", "transformers.models.m2m_100", "transformers.models.mamba", "transformers.models.mamba2", "transformers.models.marian", "transformers.models.markuplm", "transformers.models.mask2former", "transformers.models.maskformer", "transformers.models.mbart", "transformers.models.mbart50", "transformers.models.megatron_bert", "transformers.models.megatron_gpt2", "transformers.models.mgp_str", "transformers.models.mimi", "transformers.models.minimax", "transformers.models.mistral", "transformers.models.mistral3", "transformers.models.mixtral", "transformers.models.mlcd", "transformers.models.mllama", "transformers.models.mluke", "transformers.models.mobilebert", "transformers.models.mobilenet_v1", "transformers.models.mobilenet_v2", "transformers.models.mobilevit", "transformers.models.mobilevitv2", "transformers.models.modernbert", "transformers.models.moonshine", "transformers.models.moshi", "transformers.models.mpnet", "transformers.models.mpt", "transformers.models.mra", "transformers.models.mt5", "transformers.models.musicgen", "transformers.models.musicgen_melody", "transformers.models.mvp", "transformers.models.myt5", "transformers.models.nemotron", "transformers.models.nllb", "transformers.models.nllb_moe", "transformers.models.nougat", "transformers.models.nystromformer", "transformers.models.olmo", "transformers.models.olmo2", "transformers.models.olmoe", "transformers.models.omdet_turbo", "transformers.models.oneformer", "transformers.models.openai", "transformers.models.opt", "transformers.models.owlv2", "transformers.models.owlvit", "transformers.models.paligemma", "transformers.models.patchtsmixer", "transformers.models.patchtst", "transformers.models.pegasus", "transformers.models.pegasus_x", "transformers.models.perceiver", "transformers.models.persimmon", "transformers.models.phi", "transformers.models.phi3", "transformers.models.phi4_multimodal", "transformers.models.phimoe", "transformers.models.phobert", "transformers.models.pix2struct", "transformers.models.pixtral", "transformers.models.plbart", "transformers.models.poolformer", "transformers.models.pop2piano", "transformers.models.prompt_depth_anything", "transformers.models.prophetnet", "transformers.models.pvt", "transformers.models.pvt_v2", "transformers.models.qwen2", "transformers.models.qwen2_5_vl", "transformers.models.qwen2_audio", "transformers.models.qwen2_moe", "transformers.models.qwen2_vl", "transformers.models.qwen3", "transformers.models.qwen3_moe", "transformers.models.rag", "transformers.models.recurrent_gemma", "transformers.models.reformer", "transformers.models.regnet", "transformers.models.rembert", "transformers.models.resnet", "transformers.models.roberta", "transformers.models.roberta_prelayernorm", "transformers.models.roc_bert", "transformers.models.roformer", "transformers.models.rt_detr", "transformers.models.rt_detr_v2", "transformers.models.rwkv", "transformers.models.sam", "transformers.models.sam_hq", "transformers.models.seamless_m4t", "transformers.models.seamless_m4t_v2", "transformers.models.segformer", "transformers.models.seggpt", "transformers.models.sew", "transformers.models.sew_d", "transformers.models.shieldgemma2", "transformers.models.siglip", "transformers.models.siglip2", "transformers.models.smolvlm", "transformers.models.speech_encoder_decoder", "transformers.models.speech_to_text", "transformers.models.speecht5", "transformers.models.splinter", "transformers.models.squeezebert", "transformers.models.stablelm", "transformers.models.starcoder2", "transformers.models.superglue", "transformers.models.superpoint", "transformers.models.swiftformer", "transformers.models.swin", "transformers.models.swin2sr", "transformers.models.swinv2", "transformers.models.switch_transformers", "transformers.models.t5", "transformers.models.t5gemma", "transformers.models.table_transformer", "transformers.models.tapas", "transformers.models.textnet", "transformers.models.time_series_transformer", "transformers.models.timesfm", "transformers.models.timesformer", "transformers.models.timm_backbone", "transformers.models.timm_wrapper", "transformers.models.trocr", "transformers.models.tvp", "transformers.models.udop", "transformers.models.umt5", "transformers.models.unispeech", "transformers.models.unispeech_sat", "transformers.models.univnet", "transformers.models.upernet", "transformers.models.video_llava", "transformers.models.videomae", "transformers.models.vilt", "transformers.models.vipllava", "transformers.models.vision_encoder_decoder", "transformers.models.vision_text_dual_encoder", "transformers.models.visual_bert", "transformers.models.vit", "transformers.models.vit_mae", "transformers.models.vit_msn", "transformers.models.vitdet", "transformers.models.vitmatte", "transformers.models.vitpose", "transformers.models.vitpose_backbone", "transformers.models.vits", "transformers.models.vivit", "transformers.models.vjepa2", "transformers.models.wav2vec2", "transformers.models.wav2vec2_bert", "transformers.models.wav2vec2_conformer", "transformers.models.wav2vec2_phoneme", "transformers.models.wav2vec2_with_lm", "transformers.models.wavlm", "transformers.models.whisper", "transformers.models.x_clip", "transformers.models.xglm", "transformers.models.xlm", "transformers.models.xlm_roberta", "transformers.models.xlm_roberta_xl", "transformers.models.xlnet", "transformers.models.xmod", "transformers.models.yolos", "transformers.models.yoso", "transformers.models.zamba", "transformers.models.zamba2", "transformers.models.zoedepth", "transformers.utils", "typing", "builtins", "torch.distributed", "transformers.utils.logging", "torch", "_frozen_importlib", "abc"], "hash": "f81d536196cd2a5a31c953677e3751cfad028088", "id": "transformers.models", "ignore_all": true, "interface_hash": "25cbf8d087e7dfcb7afc7a5c4d9bd79e63691584", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\__init__.py", "plugin_data": null, "size": 10201, "suppressed": [], "version_id": "1.16.1"}