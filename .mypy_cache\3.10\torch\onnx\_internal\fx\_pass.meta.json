{"data_mtime": 1752081394, "dep_lines": [16, 16, 16, 15, 14, 20, 2, 4, 5, 6, 7, 8, 9, 10, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 10, 25, 5, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.fx.diagnostics", "torch.onnx._internal.fx.onnxfunction_dispatcher", "torch.onnx._internal.fx", "torch._subclasses.fake_tensor", "torch.fx", "torch._subclasses", "__future__", "abc", "contextlib", "dataclasses", "difflib", "io", "logging", "sys", "typing", "torch", "builtins", "_frozen_importlib", "enum", "functools", "torch.fx.graph", "torch.fx.graph_module", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.onnx._internal.diagnostics", "torch.onnx._internal.diagnostics._rules", "torch.onnx._internal.diagnostics.infra", "torch.onnx._internal.diagnostics.infra._infra", "torch.onnx._internal.diagnostics.infra.context", "torch.utils", "torch.utils._python_dispatch", "types"], "hash": "85bd8cd0c5c418f2ffa3da078af9dc44a1dc627c", "id": "torch.onnx._internal.fx._pass", "ignore_all": true, "interface_hash": "f327aefe2c12da8e0ce370fb6a06b4484a2b9229", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\fx\\_pass.py", "plugin_data": null, "size": 12781, "suppressed": [], "version_id": "1.16.1"}