{"data_mtime": 1752081394, "dep_lines": [39, 32, 21, 22, 23, 32, 16, 18, 20, 36, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 10, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.superpoint.modeling_superpoint", "transformers.utils.logging", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "typing", "numpy", "transformers", "torch", "PIL", "builtins", "PIL.Image", "_frozen_importlib", "abc", "collections", "enum", "functools", "logging", "torch._C", "torch._tensor", "transformers.feature_extraction_utils", "transformers.image_processing_base", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "typing_extensions"], "hash": "b2567ae562ef65ca0b7cec5e57bb687afc9a1fa9", "id": "transformers.models.superpoint.image_processing_superpoint", "ignore_all": true, "interface_hash": "7ca5a7590221c702538615edf1381e5103b7e437", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\superpoint\\image_processing_superpoint.py", "plugin_data": null, "size": 15906, "suppressed": [], "version_id": "1.16.1"}