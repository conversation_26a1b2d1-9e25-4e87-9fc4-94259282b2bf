{"data_mtime": 1752082282, "dep_lines": [11, 6, 5, 7, 8, 9, 194, 195, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["setup_agent.core.config", "logging.handlers", "logging", "sys", "pathlib", "typing", "time", "functools", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "os", "setup_agent.core", "types"], "hash": "1e93388868517d501ec06c0f27922955a6573f92", "id": "setup_agent.utils.logging_utils", "ignore_all": false, "interface_hash": "b14b5221f61488c055f00483ffad1557796b2c3a", "mtime": 1752081426, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "F:\\SetupAgent\\setup_agent\\utils\\logging_utils.py", "plugin_data": null, "size": 6986, "suppressed": [], "version_id": "1.16.1"}