{"data_mtime": 1752081392, "dep_lines": [22, 21, 22, 19, 1, 1, 1, 1, 1, 1, 1, 1, 26], "dep_prios": [10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.utils.logging", "transformers.feature_extraction_utils", "transformers.utils", "html", "builtins", "_frozen_importlib", "abc", "collections", "logging", "transformers.utils.hub", "transformers.utils.import_utils", "typing"], "hash": "f335931b6698fd769d382abc9257660b86c6a022", "id": "transformers.models.markuplm.feature_extraction_markuplm", "ignore_all": true, "interface_hash": "67b51023bdddaf666d5449c7ce75e0d97a81c469", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\markuplm\\feature_extraction_markuplm.py", "plugin_data": null, "size": 6449, "suppressed": ["bs4"], "version_id": "1.16.1"}