"""lazy_embeddings.py: Lazy wrapper for embedding operations with improved import resolution and type safety."""
from utils.embeddings import EmbeddingManager


class LazyEmbeddings:
    def __init__(self):
        """
        Initialize the EmbeddingManager instance.
        Raises ImportError if the module path is incorrect.
        """
        # Get default configuration - you may want to pass this as a parameter
        config_dict = {}  # Default empty config, can be enhanced later
        # Instantiation will raise if EmbeddingManager is unavailable
        self.embedding_manager: EmbeddingManager = EmbeddingManager(config_dict)
    def store_interaction(self, user_input: str, response: str, metadata: dict = None) -> None:
        """Store interaction text into embeddings database."""
        # Use the EmbeddingManager's store_interaction method with proper parameters
        self.embedding_manager.store_interaction(user_input, response, metadata or {})

    def search_similar(self, query: str, top_k: int = 5) -> list:
        """Return top-k similar embeddings to the query."""
        return self.embedding_manager.find_similar_interactions(query, top_k)

    def cleanup_old_data(self, older_than_seconds: int) -> None:
        """Remove embeddings older than the provided threshold."""
        # Convert seconds to days for the existing API
        days = older_than_seconds // (24 * 3600)
        if hasattr(self.embedding_manager, 'cleanup_old_data'):
            self.embedding_manager.cleanup_old_data(days)

    def get_stats(self) -> dict:
        """Retrieve statistics about the embeddings store."""
        if hasattr(self.embedding_manager, 'get_stats'):
            return self.embedding_manager.get_stats()
        return {"available": True, "stats": "Not implemented"}


# Global lazy embeddings instance for backward compatibility
lazy_embeddings = LazyEmbeddings()

# Usage example:
# lazy = LazyEmbeddings()
# lazy.store_interaction("Hello world")
# results = lazy.search_similar("world")
# stats = lazy.get_stats()
