{"data_mtime": 1752081394, "dep_lines": [29, 29, 28, 28, 28, 29, 23, 27, 28, 22, 24, 26, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 20, 5, 10, 20, 10, 5, 10, 5, 30, 30], "dependencies": ["torch.onnx._internal.jit_utils", "torch.onnx._internal.registration", "torch.onnx._type_utils", "torch.onnx.symbolic_helper", "torch.onnx.symbolic_opset9", "torch.onnx._internal", "collections.abc", "torch._C", "torch.onnx", "functools", "typing", "torch", "builtins", "_frozen_importlib", "abc"], "hash": "d95034234036545df98775a3a3dcf6f04c291f1e", "id": "torch.onnx.symbolic_opset18", "ignore_all": true, "interface_hash": "d7ee082a320833ae7f873e52e731b6c59cc0be4c", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\symbolic_opset18.py", "plugin_data": null, "size": 8357, "suppressed": [], "version_id": "1.16.1"}