{"data_mtime": 1752081392, "dep_lines": [28, 39, 1044, 23, 26, 27, 28, 1044, 17, 18, 19, 20, 21, 23, 25, 263, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 5, 5, 5, 20, 10, 10, 10, 10, 5, 20, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.utils.generic", "transformers.models.auto", "packaging.version", "transformers.dynamic_module_utils", "transformers.modeling_gguf_pytorch_utils", "transformers.utils", "transformers.models", "copy", "json", "os", "warnings", "typing", "packaging", "transformers", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "logging", "transformers.utils.doc", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "1abe94d4975eef730ddf50d434b46a3203efff25", "id": "transformers.configuration_utils", "ignore_all": true, "interface_hash": "86e7d7aa49efb3a3707395444d09231d2c1f5888", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\configuration_utils.py", "plugin_data": null, "size": 59443, "suppressed": [], "version_id": "1.16.1"}