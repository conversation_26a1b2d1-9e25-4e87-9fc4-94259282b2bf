{"data_mtime": 1752081394, "dep_lines": [35, 36, 29, 30, 33, 34, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1457, 31], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 5], "dependencies": ["transformers.generation.configuration_utils", "transformers.utils.metrics", "torch.nn", "torch.profiler", "transformers.cache_utils", "transformers.configuration_utils", "logging", "queue", "statistics", "threading", "time", "abc", "collections", "dataclasses", "enum", "functools", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "torch._C", "torch._C._VariableFunctions", "torch._<PERSON>._profiler", "torch._dynamo", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.cuda", "torch.cuda.graphs", "torch.cuda.streams", "torch.profiler.profiler", "torch.utils", "torch.utils._contextlib", "transformers.utils", "transformers.utils.hub", "types", "typing_extensions"], "hash": "8fb36d26b6032c12d9dcf0bc8ac24a3590a0fddf", "id": "transformers.generation.continuous_batching", "ignore_all": true, "interface_hash": "3ddcaeebea85252113e94ec04f00e948e48d00dd", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\generation\\continuous_batching.py", "plugin_data": null, "size": 61296, "suppressed": ["tqdm.contrib.logging", "tqdm"], "version_id": "1.16.1"}