{"data_mtime": 1752081394, "dep_lines": [37, 25, 30, 36, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22, 19, 21, 23, 19, 20], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10, 5, 20, 10], "dependencies": ["transformers.models.resnet.configuration_resnet", "transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "functools", "typing", "builtins", "_frozen_importlib", "abc", "collections", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.utils.backbone_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "cbf4f26a9b5639951022e065af83862f5e3a686e", "id": "transformers.models.resnet.modeling_flax_resnet", "ignore_all": true, "interface_hash": "752d9a0ef1c59db74a507c3a79d9051d698d18a3", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\resnet\\modeling_flax_resnet.py", "plugin_data": null, "size": 24708, "suppressed": ["flax.core.frozen_dict", "flax.linen", "jax.numpy", "flax.traverse_util", "flax", "jax"], "version_id": "1.16.1"}