{"data_mtime": 1752081604, "dep_lines": [28, 510, 26, 27, 28, 42, 510, 16, 17, 18, 19, 20, 21, 23, 24, 42, 510, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 5, 10, 20, 10, 10, 10, 10, 5, 5, 10, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.models.auto", "transformers.dynamic_module_utils", "transformers.feature_extraction_utils", "transformers.utils", "PIL.Image", "transformers.models", "copy", "json", "os", "warnings", "io", "typing", "numpy", "requests", "PIL", "transformers", "builtins", "_frozen_importlib", "abc", "collections", "functools", "logging", "transformers.utils.doc", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "f440c8e14fb7b9c4eabc99f6a1238fccf9eaef59", "id": "transformers.image_processing_base", "ignore_all": true, "interface_hash": "9370806e717c4df0cfad9b5457112b5c8445f456", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\image_processing_base.py", "plugin_data": null, "size": 24648, "suppressed": [], "version_id": "1.16.1"}