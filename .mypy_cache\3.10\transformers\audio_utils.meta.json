{"data_mtime": 1752081602, "dep_lines": [27, 19, 20, 21, 22, 24, 25, 1, 1, 1, 1, 1, 1, 36], "dep_prios": [5, 10, 10, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 10], "dependencies": ["transformers.utils", "os", "warnings", "io", "typing", "numpy", "requests", "builtins", "_frozen_importlib", "abc", "numpy._typing", "numpy._typing._nbit_base", "transformers.utils.import_utils"], "hash": "244a24271db7b1f339d9ec23375ed29decdd42cd", "id": "transformers.audio_utils", "ignore_all": true, "interface_hash": "33d6ddeafc53bfc9532fed9257d825dffaab8809", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\audio_utils.py", "plugin_data": null, "size": 54287, "suppressed": ["librosa"], "version_id": "1.16.1"}