{"data_mtime": 1752081391, "dep_lines": [30, 16, 22, 29, 15, 17, 18, 20, 40, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 51, 48, 51], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20], "dependencies": ["transformers.utils.import_utils", "collections.abc", "transformers.image_utils", "transformers.utils", "collections", "math", "typing", "numpy", "PIL", "torch", "builtins", "PIL.Image", "_frozen_importlib", "abc", "enum", "functools", "numpy._typing", "numpy._typing._nbit_base", "torch._C", "torch._tensor", "transformers.utils.generic", "typing_extensions"], "hash": "ace280a024ac2d92f67828d9283383b427b19aed", "id": "transformers.image_transforms", "ignore_all": true, "interface_hash": "8053c30e9217121d22312860711f97b930a4f057", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\image_transforms.py", "plugin_data": null, "size": 41526, "suppressed": ["jax.numpy", "tensorflow", "jax"], "version_id": "1.16.1"}