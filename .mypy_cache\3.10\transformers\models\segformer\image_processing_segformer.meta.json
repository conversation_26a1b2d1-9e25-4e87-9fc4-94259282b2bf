{"data_mtime": 1752081394, "dep_lines": [36, 44, 45, 21, 22, 23, 36, 49, 17, 19, 49, 52, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 10, 5, 10, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.utils.deprecation", "transformers.utils.import_utils", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "PIL.Image", "typing", "numpy", "PIL", "torch", "builtins", "_frozen_importlib", "abc", "enum", "functools", "logging", "torch._C", "torch._tensor", "transformers.image_processing_base", "transformers.utils.constants", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "3b0cc9bec4dea3e0472ec8e510f2431c5067efdf", "id": "transformers.models.segformer.image_processing_segformer", "ignore_all": true, "interface_hash": "d71a54b13694b5b24f1f6b5d7038a6453c1eafac", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\segformer\\image_processing_segformer.py", "plugin_data": null, "size": 22940, "suppressed": [], "version_id": "1.16.1"}