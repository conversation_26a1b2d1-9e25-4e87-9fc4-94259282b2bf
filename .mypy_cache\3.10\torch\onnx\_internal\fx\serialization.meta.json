{"data_mtime": 1752081394, "dep_lines": [10, 10, 16, 2, 4, 5, 6, 7, 9, 1, 1, 1, 1, 1, 14], "dep_prios": [10, 20, 25, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 25], "dependencies": ["torch.onnx._type_utils", "torch.onnx", "torch.types", "__future__", "io", "logging", "os", "typing", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._tensor"], "hash": "f78ca59d1d386b5d922336e16f1da2e32a21324b", "id": "torch.onnx._internal.fx.serialization", "ignore_all": true, "interface_hash": "8bcd93de4d5d0c02b3fbadbda8cbfbe9c7092d50", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\fx\\serialization.py", "plugin_data": null, "size": 11868, "suppressed": ["onnx"], "version_id": "1.16.1"}