{"data_mtime": 1752081394, "dep_lines": [6, 6, 7, 7, 6, 7, 4, 5, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 20, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.exporter._decomp", "torch.onnx._internal.exporter._registration", "torch.onnx._internal.fx.diagnostics", "torch.onnx._internal.fx.passes", "torch.onnx._internal.exporter", "torch.onnx._internal.fx", "torch.export", "torch.fx", "__future__", "torch", "builtins", "_frozen_importlib", "abc", "torch.export.exported_program", "torch.fx.graph_module", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "typing"], "hash": "9ec36282e42345cd2eadda074b471a08a7be44b0", "id": "torch.onnx._internal.exporter._fx_passes", "ignore_all": true, "interface_hash": "0e29c56e1b6074e4456fc1b123ccd2c5fa4b14c3", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_fx_passes.py", "plugin_data": null, "size": 1939, "suppressed": [], "version_id": "1.16.1"}