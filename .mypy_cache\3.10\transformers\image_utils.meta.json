{"data_mtime": 1752081604, "dep_lines": [26, 39, 17, 24, 26, 50, 51, 15, 16, 18, 19, 20, 22, 23, 24, 50, 74, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 59], "dep_prios": [10, 5, 5, 10, 5, 10, 10, 10, 10, 5, 5, 5, 10, 10, 20, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.utils.logging", "transformers.utils.constants", "collections.abc", "packaging.version", "transformers.utils", "PIL.Image", "PIL.ImageOps", "base64", "os", "dataclasses", "io", "typing", "numpy", "requests", "packaging", "PIL", "torch", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "functools", "logging", "torch._C", "torch._tensor", "transformers.utils.generic", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "e9a000d1c78efe8ecaa648f72c2b8deee9f73690", "id": "transformers.image_utils", "ignore_all": true, "interface_hash": "22915332671b03f62799e629d2fca773303197cf", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\image_utils.py", "plugin_data": null, "size": 36955, "suppressed": ["torchvision.transforms"], "version_id": "1.16.1"}