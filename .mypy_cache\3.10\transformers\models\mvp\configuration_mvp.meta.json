{"data_mtime": 1752081393, "dep_lines": [20, 19, 20, 17, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.configuration_utils", "transformers.utils", "warnings", "builtins", "_frozen_importlib", "_warnings", "abc", "logging", "transformers.utils.hub", "typing"], "hash": "be5dc9f596e51863c5af16c5c50a10d323714761", "id": "transformers.models.mvp.configuration_mvp", "ignore_all": true, "interface_hash": "913fccb42659c87aad48b2d7b26d9a51ce9d03ac", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\mvp\\configuration_mvp.py", "plugin_data": null, "size": 8451, "suppressed": [], "version_id": "1.16.1"}