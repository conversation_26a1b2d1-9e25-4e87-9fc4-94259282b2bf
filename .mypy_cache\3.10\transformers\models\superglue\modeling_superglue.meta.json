{"data_mtime": 1752081394, "dep_lines": [24, 27, 28, 21, 26, 27, 16, 17, 18, 20, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 5, 5, 10, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.superglue.configuration_superglue", "transformers.utils.logging", "transformers.models.auto", "torch.nn", "transformers.pytorch_utils", "transformers.utils", "math", "dataclasses", "typing", "torch", "transformers", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "logging", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.activation", "torch.nn.modules.batchnorm", "torch.nn.modules.container", "torch.nn.modules.dropout", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.sparse", "torch.nn.parameter", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.modeling_utils", "transformers.models.auto.auto_factory", "transformers.models.auto.modeling_auto", "transformers.models.superpoint", "transformers.models.superpoint.configuration_superpoint", "transformers.utils.args_doc", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "dfa738e3b2cc1e7f9934642d381b6d82b7462db6", "id": "transformers.models.superglue.modeling_superglue", "ignore_all": true, "interface_hash": "8afe055b019ee9325d1f11e2e47ce435f9ce264c", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\superglue\\modeling_superglue.py", "plugin_data": null, "size": 37928, "suppressed": [], "version_id": "1.16.1"}