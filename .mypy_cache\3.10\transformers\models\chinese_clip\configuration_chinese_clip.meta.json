{"data_mtime": 1752081393, "dep_lines": [28, 18, 23, 24, 26, 27, 17, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 20, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "collections.abc", "transformers.processing_utils", "transformers.utils", "transformers.configuration_utils", "transformers.onnx", "collections", "typing", "builtins", "_frozen_importlib", "abc", "enum", "logging", "transformers.onnx.config", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "d7d7b41c6cd8b8cb7b161865073da16410e19ea9", "id": "transformers.models.chinese_clip.configuration_chinese_clip", "ignore_all": true, "interface_hash": "6ca7a77a469d613d7272c07b2951907c5fa117cb", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\chinese_clip\\configuration_chinese_clip.py", "plugin_data": null, "size": 20839, "suppressed": [], "version_id": "1.16.1"}