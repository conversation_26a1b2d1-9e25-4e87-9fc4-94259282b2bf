{"data_mtime": 1752081392, "dep_lines": [17, 20, 32, 14, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, 28, 24], "dep_prios": [10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["torch.nn", "transformers.utils", "transformers.image_transforms", "typing", "torch", "builtins", "_frozen_importlib", "abc", "functools", "torch._C", "torch._C._VariableFunctions", "torch._jit_internal", "torch._tensor", "torch.autograd", "torch.autograd.grad_mode", "torch.jit", "torch.nn.modules", "torch.nn.modules.module", "torch.utils", "torch.utils._contextlib", "transformers.utils.import_utils", "types"], "hash": "c8817fe7a57995dde2d06ddfcda905c43ac04d15", "id": "transformers.loss.loss_for_object_detection", "ignore_all": true, "interface_hash": "b1b0993eb1286835042986305e1ff7952a00f1a7", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\loss\\loss_for_object_detection.py", "plugin_data": null, "size": 24595, "suppressed": ["accelerate.utils", "scipy.optimize", "accelerate"], "version_id": "1.16.1"}