{"data_mtime": 1752081394, "dep_lines": [19, 18, 16, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.utils.logging", "transformers.configuration_utils", "transformers.modeling_rope_utils", "transformers.utils", "builtins", "_frozen_importlib", "_typeshed", "abc", "logging", "transformers.models.auto", "transformers.utils.hub", "types", "typing"], "hash": "7734c95ca7907f862e79f35b7d6ee4d8e82e3f93", "id": "transformers.models.csm.configuration_csm", "ignore_all": true, "interface_hash": "7bdad684c62d6cbff512f6ae9d3169001b4e1866", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\csm\\configuration_csm.py", "plugin_data": null, "size": 23791, "suppressed": [], "version_id": "1.16.1"}