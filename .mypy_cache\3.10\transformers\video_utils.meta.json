{"data_mtime": 1752081604, "dep_lines": [30, 18, 23, 28, 29, 30, 47, 48, 16, 17, 19, 20, 21, 22, 25, 26, 47, 54, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 51], "dep_prios": [10, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 10, 10, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.utils.logging", "collections.abc", "urllib.parse", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "PIL.Image", "PIL.ImageOps", "os", "warnings", "contextlib", "dataclasses", "io", "typing", "numpy", "requests", "PIL", "torch", "builtins", "_frozen_importlib", "abc", "enum", "functools", "logging", "torch._C", "torch._tensor", "transformers.utils.generic", "transformers.utils.import_utils", "typing_extensions"], "hash": "281f3c868f39254f403819ab733b45ba1ce9582a", "id": "transformers.video_utils", "ignore_all": true, "interface_hash": "7c431d0c2e2faaafe128dad9608f0621eafe7679", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\video_utils.py", "plugin_data": null, "size": 30429, "suppressed": ["torchvision"], "version_id": "1.16.1"}