{"data_mtime": **********, "dep_lines": [7, 8, 10, 11, 12, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["setup_agent.core.config", "setup_agent.core.exceptions", "setup_agent.llm.base", "setup_agent.llm.ollama_provider", "setup_agent.llm.openai_provider", "logging", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "setup_agent.core", "types"], "hash": "585cc5c3c320194cfda6437626f19c9c64e3ac2a", "id": "setup_agent.llm.factory", "ignore_all": false, "interface_hash": "94fb08be6a630106eb1b45d2949407f461bf3987", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\llm\\factory.py", "plugin_data": null, "size": 8193, "suppressed": [], "version_id": "1.16.1"}