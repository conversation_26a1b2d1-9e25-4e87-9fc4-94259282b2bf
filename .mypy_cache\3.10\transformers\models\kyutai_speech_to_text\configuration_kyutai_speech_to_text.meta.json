{"data_mtime": 1752081394, "dep_lines": [18, 17, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.utils.logging", "transformers.configuration_utils", "transformers.utils", "builtins", "_frozen_importlib", "_typeshed", "abc", "logging", "transformers.models.auto", "transformers.utils.hub", "types", "typing"], "hash": "7c3655aa4d4a1b191412602b504bb547ebd03088", "id": "transformers.models.kyutai_speech_to_text.configuration_kyutai_speech_to_text", "ignore_all": true, "interface_hash": "1b2ceab4108ac3b01e7ca4cc4fe8c2bc72e1b5f9", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\kyutai_speech_to_text\\configuration_kyutai_speech_to_text.py", "plugin_data": null, "size": 9014, "suppressed": [], "version_id": "1.16.1"}