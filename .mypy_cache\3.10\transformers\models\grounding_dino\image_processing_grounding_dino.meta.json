{"data_mtime": 1752081394, "dep_lines": [82, 54, 20, 25, 26, 27, 38, 54, 71, 17, 18, 19, 21, 23, 70, 75, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 78, 79, 78], "dep_prios": [25, 10, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 10, 20], "dependencies": ["transformers.models.grounding_dino.modeling_grounding_dino", "transformers.utils.logging", "collections.abc", "transformers.feature_extraction_utils", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "torch.nn", "io", "pathlib", "collections", "typing", "numpy", "torch", "PIL", "builtins", "PIL.Image", "_frozen_importlib", "_typeshed", "abc", "enum", "functools", "logging", "os", "torch._C", "torch._tensor", "transformers.image_processing_base", "transformers.utils.constants", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "typing_extensions"], "hash": "9951ce2d5dfb2603b084f6733bbb34f52c3b6b91", "id": "transformers.models.grounding_dino.image_processing_grounding_dino", "ignore_all": true, "interface_hash": "e4cb019dd27ffdb989a8e4f56991c9393b165c43", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\grounding_dino\\image_processing_grounding_dino.py", "plugin_data": null, "size": 72286, "suppressed": ["scipy.special", "scipy.stats", "scipy"], "version_id": "1.16.1"}