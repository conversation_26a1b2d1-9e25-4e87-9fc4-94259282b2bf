{"data_mtime": **********, "dep_lines": [4, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["huggingface_hub.inference._providers._common", "huggingface_hub.hf_api", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "4b8614d52f60e6b08152620e8c1863027faaf028", "id": "huggingface_hub.inference._providers.openai", "ignore_all": true, "interface_hash": "02660206a0e36d3952d609ac4982815acf2a6305", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\huggingface_hub\\inference\\_providers\\openai.py", "plugin_data": null, "size": 1089, "suppressed": [], "version_id": "1.16.1"}