{"data_mtime": 1752081394, "dep_lines": [27, 33, 39, 17, 18, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 23, 20, 22, 24, 20, 21], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 10, 5, 20, 10], "dependencies": ["transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "functools", "typing", "transformers", "builtins", "_frozen_importlib", "_typeshed", "abc", "collections", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.models.regnet.configuration_regnet", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "d510c687a05cb7b99be721516276802ded7cf617", "id": "transformers.models.regnet.modeling_flax_regnet", "ignore_all": true, "interface_hash": "cbd9c9dbe6e8b1c44202de8301ecdb8cba94e0a8", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\regnet\\modeling_flax_regnet.py", "plugin_data": null, "size": 28519, "suppressed": ["flax.core.frozen_dict", "flax.linen", "jax.numpy", "flax.traverse_util", "flax", "jax"], "version_id": "1.16.1"}