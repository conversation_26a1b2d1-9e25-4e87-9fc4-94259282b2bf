{"data_mtime": 1752081391, "dep_lines": [33, 16, 26, 33, 14, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.integrations", "transformers.trainer_utils", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc", "enum", "logging", "transformers.utils.generic", "typing_extensions"], "hash": "74ae0b4c904797c40d00beff0971d1c446f9f8c3", "id": "transformers.hyperparameter_search", "ignore_all": true, "interface_hash": "ae059fa9e48a688346d3c83ffacb2e8412defd51", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\hyperparameter_search.py", "plugin_data": null, "size": 4194, "suppressed": [], "version_id": "1.16.1"}