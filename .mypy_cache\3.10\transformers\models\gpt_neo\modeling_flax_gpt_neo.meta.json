{"data_mtime": 1752081394, "dep_lines": [31, 30, 28, 29, 30, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 22, 24, 19, 21, 25, 19, 20], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 10, 5, 20, 5], "dependencies": ["transformers.models.gpt_neo.configuration_gpt_neo", "transformers.utils.logging", "transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "functools", "typing", "builtins", "_frozen_importlib", "abc", "collections", "logging", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "965e32b8260b641ebe7e1f9273d7db61ae3e396a", "id": "transformers.models.gpt_neo.modeling_flax_gpt_neo", "ignore_all": true, "interface_hash": "4dbaeaed6ab8e7bf7bd91f9b599160177ba68fd5", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\gpt_neo\\modeling_flax_gpt_neo.py", "plugin_data": null, "size": 28175, "suppressed": ["flax.core.frozen_dict", "flax.linen.attention", "flax.linen", "jax.numpy", "flax.traverse_util", "flax", "jax"], "version_id": "1.16.1"}