{"data_mtime": 1752081394, "dep_lines": [33, 39, 41, 22, 24, 25, 31, 37, 38, 16, 17, 18, 20, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 29], "dep_prios": [5, 25, 5, 10, 5, 5, 5, 25, 25, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["transformers.generation.logits_process", "transformers.generation.configuration_utils", "transformers.utils.deprecation", "torch.nn", "transformers.pytorch_utils", "transformers.utils", "transformers.cache_utils", "transformers.modeling_utils", "transformers.tokenization_utils_base", "copy", "weakref", "typing", "numpy", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "numpy._core", "numpy._core.fromnumeric", "numpy._typing", "numpy._typing._array_like", "numpy._typing._nbit_base", "numpy._typing._nested_sequence", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.nn.modules", "torch.nn.modules.linear", "torch.nn.modules.module", "torch.nn.modules.sparse", "torch.types", "transformers.configuration_utils", "transformers.integrations", "transformers.integrations.peft", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "95efe04dd9ad869f334fc37332bc2be3bda741c4", "id": "transformers.generation.candidate_generator", "ignore_all": true, "interface_hash": "86fc6dc1bb37ad5c61deec42fe3c6966aa1c4fe3", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\generation\\candidate_generator.py", "plugin_data": null, "size": 63320, "suppressed": ["sklearn.metrics"], "version_id": "1.16.1"}