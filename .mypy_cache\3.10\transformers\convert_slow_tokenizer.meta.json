{"data_mtime": 1752081391, "dep_lines": [477, 28, 29, 24, 28, 21, 22, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 1594, 25, 88], "dep_prios": [20, 10, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 20, 5, 20], "dependencies": ["transformers.models.roformer.tokenization_utils", "transformers.utils.logging", "transformers.utils.import_utils", "packaging.version", "transformers.utils", "warnings", "typing", "packaging", "builtins", "_frozen_importlib", "_io", "_typeshed", "_warnings", "abc", "io", "logging", "os", "transformers.models", "transformers.models.roformer", "types", "typing_extensions"], "hash": "5bbcc90e0a8555462db4f2c22f6f03c9d5c6cbb2", "id": "transformers.convert_slow_tokenizer", "ignore_all": true, "interface_hash": "0b777df004837a7133333521973e98ef0e36c557", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\convert_slow_tokenizer.py", "plugin_data": null, "size": 63658, "suppressed": ["tokenizers.models", "tiktoken.load", "tokenizers", "sentencepiece"], "version_id": "1.16.1"}