{"data_mtime": 1752081393, "dep_lines": [24, 18, 22, 23, 24, 17, 19, 21, 202, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "collections.abc", "transformers.configuration_utils", "transformers.onnx", "transformers.utils", "collections", "typing", "transformers", "torch", "builtins", "_frozen_importlib", "abc", "enum", "logging", "transformers.onnx.config", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils.generic", "transformers.utils.hub"], "hash": "75d9838582be299a2932bb6af5195fed3cfe864a", "id": "transformers.models.codegen.configuration_codegen", "ignore_all": true, "interface_hash": "ca1190d7fea5111d03010e540f634e5c7a1a3c82", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\codegen\\configuration_codegen.py", "plugin_data": null, "size": 9574, "suppressed": [], "version_id": "1.16.1"}