{"data_mtime": 1752081392, "dep_lines": [28, 670, 27, 28, 670, 18, 19, 20, 21, 22, 23, 25, 51, 670, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 151, 119, 151], "dep_prios": [10, 20, 5, 5, 20, 10, 10, 10, 10, 5, 5, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20], "dependencies": ["transformers.utils.logging", "transformers.models.auto", "transformers.dynamic_module_utils", "transformers.utils", "transformers.models", "copy", "json", "os", "warnings", "collections", "typing", "numpy", "torch", "transformers", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "logging", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "transformers.utils.import_utils", "types", "typing_extensions"], "hash": "e7839a52d3aef0aaf5dae6bb52767c0a71507fcd", "id": "transformers.feature_extraction_utils", "ignore_all": true, "interface_hash": "2113563e6cb4e4b499dc8e0a2b85ae3502e741ac", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\feature_extraction_utils.py", "plugin_data": null, "size": 29779, "suppressed": ["jax.numpy", "tensorflow", "jax"], "version_id": "1.16.1"}