{"data_mtime": 1752081393, "dep_lines": [19, 20, 21, 22, 18, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.configuration_auto", "transformers.models.chinese_clip.configuration_chinese_clip", "transformers.models.clip.configuration_clip", "transformers.models.siglip.configuration_siglip", "transformers.utils.logging", "transformers.configuration_utils", "transformers.utils", "builtins", "_frozen_importlib", "abc", "logging", "transformers.models.auto", "transformers.models.chinese_clip", "transformers.models.clip", "transformers.models.siglip", "transformers.utils.hub", "typing"], "hash": "b2f6583ec29adf183d2fbafddf14b27a4972eb0a", "id": "transformers.models.vision_text_dual_encoder.configuration_vision_text_dual_encoder", "ignore_all": true, "interface_hash": "af629dc07c67c753537b19d43eb40e8d263bde4d", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\vision_text_dual_encoder\\configuration_vision_text_dual_encoder.py", "plugin_data": null, "size": 5023, "suppressed": [], "version_id": "1.16.1"}