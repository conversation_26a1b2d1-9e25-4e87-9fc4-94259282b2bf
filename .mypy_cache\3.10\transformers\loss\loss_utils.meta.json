{"data_mtime": 1752081394, "dep_lines": [21, 22, 23, 24, 25, 18, 15, 17, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["transformers.loss.loss_d_fine", "transformers.loss.loss_deformable_detr", "transformers.loss.loss_for_object_detection", "transformers.loss.loss_grounding_dino", "transformers.loss.loss_rt_detr", "torch.nn", "typing", "torch", "builtins", "_frozen_importlib", "abc", "torch._C", "torch._tensor"], "hash": "afd52e1f6f87bfb556f36bf540db455947a69662", "id": "transformers.loss.loss_utils", "ignore_all": true, "interface_hash": "2bf820887e524ffce26c412aa0976f50c2a19fb8", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\loss\\loss_utils.py", "plugin_data": null, "size": 6858, "suppressed": [], "version_id": "1.16.1"}