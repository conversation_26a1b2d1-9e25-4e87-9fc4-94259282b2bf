{"data_mtime": 1752081394, "dep_lines": [20, 20, 283, 19, 20, 21, 21, 28, 3, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 30], "dep_prios": [10, 10, 20, 5, 20, 10, 20, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["torch.onnx._internal.exporter._dynamic_shapes", "torch.onnx._internal.exporter._ir_passes", "torch.onnx._internal.exporter._core", "torch.onnx._internal._lazy_import", "torch.onnx._internal.exporter", "torch.utils._pytree", "torch.utils", "collections.abc", "__future__", "contextlib", "copy", "gc", "logging", "os", "tempfile", "textwrap", "warnings", "typing", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_warnings", "abc", "ntpath", "numpy", "torch._C", "torch._C._VariableFunctions", "torch._tensor", "torch.export", "torch.export.exported_program", "torch.onnx.errors", "types"], "hash": "32bf2b6cb779b1b087e17f1833acd73260913258", "id": "torch.onnx._internal.exporter._onnx_program", "ignore_all": true, "interface_hash": "d09cbfc0cf1c7bf9508edb071a8abc050768d622", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\exporter\\_onnx_program.py", "plugin_data": null, "size": 16239, "suppressed": ["onnxruntime"], "version_id": "1.16.1"}