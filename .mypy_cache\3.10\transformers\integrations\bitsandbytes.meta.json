{"data_mtime": 1752081391, "dep_lines": [9, 1, 7, 9, 22, 24, 1, 2, 3, 4, 7, 21, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 29, 30, 20, 27], "dep_prios": [10, 10, 10, 5, 10, 5, 20, 5, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10, 5], "dependencies": ["transformers.utils.logging", "importlib.metadata", "packaging.version", "transformers.utils", "torch.nn", "transformers.pytorch_utils", "importlib", "inspect", "warnings", "copy", "packaging", "torch", "builtins", "_frozen_importlib", "abc", "functools", "logging", "torch._C", "torch._tensor", "torch.nn.parameter", "transformers.utils.import_utils", "typing"], "hash": "41dc0f61a3204187a2bc55f9e70e1be3fd65f0e6", "id": "transformers.integrations.bitsandbytes", "ignore_all": true, "interface_hash": "4bea9f09e76a7d5bc1380878f22c7ed3447b0d4d", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\integrations\\bitsandbytes.py", "plugin_data": null, "size": 23928, "suppressed": ["accelerate.hooks", "accelerate.utils", "bitsandbytes", "accelerate"], "version_id": "1.16.1"}