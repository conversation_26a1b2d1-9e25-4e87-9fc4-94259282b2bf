{"data_mtime": 1752081604, "dep_lines": [24, 9, 10, 11, 20, 22, 23, 1, 2, 3, 4, 6, 22, 319, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7], "dep_prios": [10, 5, 5, 5, 5, 10, 5, 10, 10, 5, 5, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["huggingface_hub.utils.logging", "huggingface_hub.file_download", "huggingface_hub.hf_api", "huggingface_hub.repocard_data", "huggingface_hub.utils", "huggingface_hub.constants", "huggingface_hub.errors", "os", "re", "pathlib", "typing", "requests", "huggingface_hub", "jinja2", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "huggingface_hub.utils._validators", "logging", "types"], "hash": "ce61fcb187ee7f1695e30532ab4c8dfdb8f046f3", "id": "huggingface_hub.repocard", "ignore_all": true, "interface_hash": "3d48e19bf126dfcace40b64ee040361fe2d4c492", "mtime": 1752005469, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\huggingface_hub\\repocard.py", "plugin_data": null, "size": 34733, "suppressed": ["yaml"], "version_id": "1.16.1"}