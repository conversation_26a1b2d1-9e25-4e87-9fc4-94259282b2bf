{"data_mtime": 1752081391, "dep_lines": [30, 40, 20, 28, 30, 18, 19, 20, 21, 22, 24, 25, 26, 28, 44, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1958, 1959, 1960, 311, 1650, 1721, 1758, 1323, 1324, 1690, 1720, 1957], "dep_prios": [10, 5, 10, 10, 5, 10, 5, 20, 10, 10, 5, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20], "dependencies": ["transformers.utils.logging", "transformers.utils.import_utils", "importlib.metadata", "packaging.version", "transformers.utils", "copy", "dataclasses", "importlib", "json", "os", "enum", "inspect", "typing", "packaging", "torch", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "logging", "torch._C", "torch.cuda", "torch.xpu", "types", "typing_extensions"], "hash": "fa45e07dbe0a3b77cf8ef15bf5dbd9b36380e827", "id": "transformers.utils.quantization_config", "ignore_all": true, "interface_hash": "b23225e9493cb4301bfabc93b294f73e4aada568", "mtime": 1752005481, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\utils\\quantization_config.py", "plugin_data": null, "size": 90185, "suppressed": ["quark.torch.export.config.config", "quark.torch.export.main_export.quant_config_parser", "quark.torch.quantization.config.config", "hqq.core.quantize", "torchao.quantization.quant_api", "torchao.quantization.quant_primitives", "torchao.core.config", "compressed_tensors.config", "compressed_tensors.quantization", "torchao.quantization", "torchao.dtypes", "quark"], "version_id": "1.16.1"}