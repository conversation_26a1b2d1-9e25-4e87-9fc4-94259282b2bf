{"data_mtime": 1752081394, "dep_lines": [38, 39, 29, 37, 24, 25, 26, 27, 28, 29, 49, 17, 18, 19, 20, 21, 22, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.auto_factory", "transformers.models.auto.configuration_auto", "transformers.utils.logging", "transformers.models.encoder_decoder", "transformers.configuration_utils", "transformers.dynamic_module_utils", "transformers.modeling_gguf_pytorch_utils", "transformers.tokenization_utils", "transformers.tokenization_utils_base", "transformers.utils", "transformers.tokenization_utils_fast", "importlib", "json", "os", "warnings", "collections", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "logging", "transformers.utils.hub", "transformers.utils.import_utils"], "hash": "b9f9b3d7a5af2cfad91297daac8559b9e3596c70", "id": "transformers.models.auto.tokenization_auto", "ignore_all": true, "interface_hash": "bc973e11307e17710a3a893328b547604b6a9e9f", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\auto\\tokenization_auto.py", "plugin_data": null, "size": 53731, "suppressed": [], "version_id": "1.16.1"}