{"data_mtime": 1752081391, "dep_lines": [159, 160, 167, 173, 182, 194, 195, 196, 197, 198, 205, 206, 207, 213, 254, 255, 256, 257, 265, 273, 285, 16, 14, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["transformers.integrations.aqlm", "transformers.integrations.awq", "transformers.integrations.bitnet", "transformers.integrations.bitsandbytes", "transformers.integrations.deepspeed", "transformers.integrations.eetq", "transformers.integrations.fbgemm_fp8", "transformers.integrations.finegrained_fp8", "transformers.integrations.fsdp", "transformers.integrations.ggml", "transformers.integrations.higgs", "transformers.integrations.hqq", "transformers.integrations.hub_kernels", "transformers.integrations.integration_utils", "transformers.integrations.peft", "transformers.integrations.quanto", "transformers.integrations.spqr", "transformers.integrations.vptq", "transformers.integrations.executorch", "transformers.integrations.tensor_parallel", "transformers.integrations.flex_attention", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc", "functools", "transformers.utils.import_utils"], "hash": "83a2018bb07eebfe0de99ad2e55838e47cc587df", "id": "transformers.integrations", "ignore_all": true, "interface_hash": "d121b3c36c09255816e9e8ce840d39cad320b9c4", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\integrations\\__init__.py", "plugin_data": null, "size": 8982, "suppressed": [], "version_id": "1.16.1"}