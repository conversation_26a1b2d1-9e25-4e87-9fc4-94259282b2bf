{"data_mtime": 1752081391, "dep_lines": [28, 29, 22, 27, 16, 17, 19, 33, 1, 1, 1, 1, 1, 20], "dep_prios": [5, 5, 5, 5, 10, 5, 10, 10, 5, 30, 30, 30, 30, 5], "dependencies": ["transformers.utils.import_utils", "transformers.utils.logging", "transformers.integrations", "transformers.utils", "re", "typing", "numpy", "torch", "builtins", "_frozen_importlib", "abc", "logging", "transformers.integrations.ggml"], "hash": "04fcc9500f8e503e3594a30282327cfd2a078431", "id": "transformers.modeling_gguf_pytorch_utils", "ignore_all": true, "interface_hash": "82c487b2096b4f0e75c02e1b4db63d5794969ce2", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\modeling_gguf_pytorch_utils.py", "plugin_data": null, "size": 20411, "suppressed": ["tqdm.auto"], "version_id": "1.16.1"}