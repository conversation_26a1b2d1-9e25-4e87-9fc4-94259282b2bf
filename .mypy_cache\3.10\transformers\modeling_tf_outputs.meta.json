{"data_mtime": 1752081390, "dep_lines": [23, 15, 17, 18, 19, 1, 1, 1, 1, 1, 21], "dep_prios": [5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 10], "dependencies": ["transformers.utils", "__future__", "warnings", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc", "collections", "transformers.utils.generic"], "hash": "804208ec59ed3d37b42bcd871781f943f1585f15", "id": "transformers.modeling_tf_outputs", "ignore_all": true, "interface_hash": "781bdf5a36bfc4d63f4cc8ef6f4d68aeb4b89775", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\modeling_tf_outputs.py", "plugin_data": null, "size": 56381, "suppressed": ["tensorflow"], "version_id": "1.16.1"}