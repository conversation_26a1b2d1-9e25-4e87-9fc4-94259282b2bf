{"data_mtime": 1752081391, "dep_lines": [39, 34, 35, 32, 34, 29, 31, 32, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["torch.nn.attention.flex_attention", "transformers.utils.logging", "transformers.utils.import_utils", "packaging.version", "transformers.utils", "typing", "torch", "packaging", "builtins", "_frozen_importlib", "abc", "logging", "torch._C", "torch._tensor", "torch.compiler", "torch.nn", "torch.nn.attention", "torch.nn.modules", "torch.nn.modules.module"], "hash": "b4ca427b85801ba69056a532d2c240d9971b071e", "id": "transformers.integrations.flex_attention", "ignore_all": true, "interface_hash": "c715ce667f89b44182ed528f3a2b0e8e8a42cdde", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\integrations\\flex_attention.py", "plugin_data": null, "size": 11295, "suppressed": [], "version_id": "1.16.1"}