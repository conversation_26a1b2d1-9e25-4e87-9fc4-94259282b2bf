{"data_mtime": 1752082282, "dep_lines": [5, 6, 11, 3, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 5, 30, 30], "dependencies": ["setup_agent.utils.logging_utils", "setup_agent.utils.file_utils", "setup_agent.utils.monitoring", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "c3f1ae4d9cdd6febcce2dc106d8b8efd9b0df2fa", "id": "setup_agent.utils", "ignore_all": false, "interface_hash": "1b9a6f48d1cf083eedbb2376d5a6a8fee83493b1", "mtime": 1752082125, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\utils\\__init__.py", "plugin_data": null, "size": 769, "suppressed": [], "version_id": "1.16.1"}