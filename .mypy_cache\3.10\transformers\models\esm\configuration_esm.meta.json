{"data_mtime": 1752081393, "dep_lines": [21, 20, 21, 17, 18, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.logging", "transformers.configuration_utils", "transformers.utils", "dataclasses", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "logging", "transformers.utils.hub", "types"], "hash": "411caa90021d13a34a423006cf97b4fd0410b638", "id": "transformers.models.esm.configuration_esm", "ignore_all": true, "interface_hash": "2e4b9692c6997bcc4a8a77bf73be5b47f1d61862", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\esm\\configuration_esm.py", "plugin_data": null, "size": 14436, "suppressed": [], "version_id": "1.16.1"}