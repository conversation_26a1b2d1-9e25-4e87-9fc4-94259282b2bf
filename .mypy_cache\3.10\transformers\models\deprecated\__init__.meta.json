{"data_mtime": 1752081391, "dep_lines": [21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 17, 16, 14, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 5, 30, 30], "dependencies": ["transformers.models.deprecated.bort", "transformers.models.deprecated.deta", "transformers.models.deprecated.efficientformer", "transformers.models.deprecated.ernie_m", "transformers.models.deprecated.gptsan_japanese", "transformers.models.deprecated.graphormer", "transformers.models.deprecated.jukebox", "transformers.models.deprecated.mctct", "transformers.models.deprecated.mega", "transformers.models.deprecated.mmbt", "transformers.models.deprecated.nat", "transformers.models.deprecated.nezha", "transformers.models.deprecated.open_llama", "transformers.models.deprecated.qdqbert", "transformers.models.deprecated.realm", "transformers.models.deprecated.retribert", "transformers.models.deprecated.speech_to_text_2", "transformers.models.deprecated.tapex", "transformers.models.deprecated.trajectory_transformer", "transformers.models.deprecated.transfo_xl", "transformers.models.deprecated.tvlt", "transformers.models.deprecated.van", "transformers.models.deprecated.vit_hybrid", "transformers.models.deprecated.xlm_prophetnet", "transformers.utils.import_utils", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "4d9a3e59c8f95f8eb02f10b40bb8a86d5e357db7", "id": "transformers.models.deprecated", "ignore_all": true, "interface_hash": "4ba1e708e2175a1fc54425e550be5398296b5e69", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\deprecated\\__init__.py", "plugin_data": null, "size": 1596, "suppressed": [], "version_id": "1.16.1"}