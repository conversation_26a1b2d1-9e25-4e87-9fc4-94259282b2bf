{"data_mtime": 1752081390, "dep_lines": [2, 3, 4, 6, 10, 73, 74, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 25, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["copy", "functools", "typing", "torch", "io", "safetensors", "transformers", "builtins", "_frozen_importlib", "_io", "abc", "os", "safetensors.torch", "torch._C", "torch._tensor", "torch.fx", "torch.fx._symbolic_trace", "torch.serialization", "torch.types", "transformers.modeling_utils", "types"], "hash": "1afe730047e9768ec1d1ac91d7a0e126b654ee9e", "id": "torch.onnx._internal.fx.patcher", "ignore_all": true, "interface_hash": "5d12a6c134e92855b09a7d36db824d022f0e67b2", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\fx\\patcher.py", "plugin_data": null, "size": 6192, "suppressed": [], "version_id": "1.16.1"}