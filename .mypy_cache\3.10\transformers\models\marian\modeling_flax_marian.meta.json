{"data_mtime": 1752081394, "dep_lines": [48, 47, 33, 40, 47, 17, 18, 19, 20, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 28, 22, 24, 29, 31, 22, 23], "dep_prios": [5, 10, 5, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 10, 5, 5, 20, 5], "dependencies": ["transformers.models.marian.configuration_marian", "transformers.utils.logging", "transformers.modeling_flax_outputs", "transformers.modeling_flax_utils", "transformers.utils", "math", "random", "functools", "typing", "numpy", "builtins", "_frozen_importlib", "abc", "collections", "logging", "transformers.configuration_utils", "transformers.generation", "transformers.generation.flax_utils", "transformers.utils.doc", "transformers.utils.generic", "transformers.utils.hub", "types", "typing_extensions"], "hash": "9e9b93d6aff95b01a060f53749c5b1646a3b7d7c", "id": "transformers.models.marian.modeling_flax_marian", "ignore_all": true, "interface_hash": "788e8b35a5ce213a3fc087f857bd278d43fd2cbe", "mtime": 1752005480, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\marian\\modeling_flax_marian.py", "plugin_data": null, "size": 64429, "suppressed": ["flax.core.frozen_dict", "flax.linen.attention", "flax.linen", "jax.numpy", "flax.traverse_util", "jax.random", "flax", "jax"], "version_id": "1.16.1"}