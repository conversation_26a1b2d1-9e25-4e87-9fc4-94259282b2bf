{"data_mtime": 1752081348, "dep_lines": [8, 9, 10, 11, 112, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["time", "logging", "functools", "typing", "random", "builtins", "_frozen_importlib", "_random", "_typeshed", "abc", "types"], "hash": "40ee809412e2ecdc51220e3fc3bab456f328c056", "id": "setup_agent.utils.retry", "ignore_all": false, "interface_hash": "e1a2b65b5e786b51a4fb9ad2957292f34dbe1263", "mtime": 1752080991, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\utils\\retry.py", "plugin_data": null, "size": 6955, "suppressed": [], "version_id": "1.16.1"}