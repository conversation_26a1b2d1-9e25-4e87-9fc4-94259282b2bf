{"data_mtime": 1752081391, "dep_lines": [21, 22, 23, 24, 25, 26, 17, 16, 14, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 5, 5, 5, 5, 30, 30], "dependencies": ["transformers.models.blenderbot_small.configuration_blenderbot_small", "transformers.models.blenderbot_small.modeling_blenderbot_small", "transformers.models.blenderbot_small.modeling_flax_blenderbot_small", "transformers.models.blenderbot_small.modeling_tf_blenderbot_small", "transformers.models.blenderbot_small.tokenization_blenderbot_small", "transformers.models.blenderbot_small.tokenization_blenderbot_small_fast", "transformers.utils.import_utils", "transformers.utils", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "33f41fc4d8ec9850f08dc868404ae06478fd005a", "id": "transformers.models.blenderbot_small", "ignore_all": true, "interface_hash": "03ff231d31f939e4704a64457b9b0ee70e25d0aa", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\blenderbot_small\\__init__.py", "plugin_data": null, "size": 1214, "suppressed": [], "version_id": "1.16.1"}