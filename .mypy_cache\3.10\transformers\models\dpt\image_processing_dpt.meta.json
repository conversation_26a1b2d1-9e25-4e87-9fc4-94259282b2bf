{"data_mtime": 1752081394, "dep_lines": [21, 47, 18, 25, 29, 30, 31, 47, 17, 19, 27, 57, 60, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 25, 5, 5, 5, 5, 10, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["transformers.utils.import_utils", "transformers.utils.logging", "collections.abc", "transformers.modeling_outputs", "transformers.image_processing_utils", "transformers.image_transforms", "transformers.image_utils", "transformers.utils", "math", "typing", "numpy", "torch", "PIL", "builtins", "PIL.Image", "_frozen_importlib", "abc", "collections", "enum", "functools", "logging", "torch._C", "torch._tensor", "transformers.image_processing_base", "transformers.utils.constants", "transformers.utils.generic", "transformers.utils.hub", "typing_extensions"], "hash": "00e1af7a08d30df435f7a5d001ba1ab21cc15ae9", "id": "transformers.models.dpt.image_processing_dpt", "ignore_all": true, "interface_hash": "cfb2f1bb2c33aab70edb8227a3e93ac0c5a359dd", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\dpt\\image_processing_dpt.py", "plugin_data": null, "size": 31707, "suppressed": [], "version_id": "1.16.1"}