{"data_mtime": 1752082282, "dep_lines": [10, 11, 12, 8, 19, 5, 6, 7, 19, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["setup_agent.llm.base", "setup_agent.core.exceptions", "setup_agent.utils.retry", "urllib.parse", "urllib.request", "json", "logging", "typing", "urllib", "requests", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "http", "http.cookiejar", "json.decoder", "json.encoder", "requests.auth", "requests.models", "setup_agent.core", "ssl", "types", "typing_extensions"], "hash": "7cb66badfc5e0bc0cd6dc2929b99171ed4d281da", "id": "setup_agent.llm.ollama_provider", "ignore_all": false, "interface_hash": "a32d01f1583c9ebb4d04db3a0e782b593e922888", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "setup_agent\\llm\\ollama_provider.py", "plugin_data": null, "size": 11767, "suppressed": [], "version_id": "1.16.1"}