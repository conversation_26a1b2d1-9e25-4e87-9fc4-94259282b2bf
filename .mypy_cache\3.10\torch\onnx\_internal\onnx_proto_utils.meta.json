{"data_mtime": 1752081394, "dep_lines": [15, 15, 12, 14, 15, 12, 13, 14, 20, 4, 6, 7, 8, 9, 11, 19, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 20, 20, 10, 20, 25, 5, 10, 10, 10, 5, 10, 25, 5, 30, 30, 30], "dependencies": ["torch.onnx._internal.jit_utils", "torch.onnx._internal.registration", "torch.jit._trace", "torch.onnx.errors", "torch.onnx._internal", "torch.jit", "torch.serialization", "torch.onnx", "collections.abc", "__future__", "glob", "os", "shutil", "typing", "torch", "io", "builtins", "_frozen_importlib", "_io", "abc"], "hash": "8938b6da654d3194ef3289556ef84d0acb2b8bc2", "id": "torch.onnx._internal.onnx_proto_utils", "ignore_all": true, "interface_hash": "f5143d84710b51224796b8c9c96c517a5c4af044", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\onnx_proto_utils.py", "plugin_data": null, "size": 9449, "suppressed": [], "version_id": "1.16.1"}