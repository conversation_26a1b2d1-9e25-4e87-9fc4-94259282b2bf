{"data_mtime": 1752081394, "dep_lines": [18, 17, 18, 12, 16, 7, 9, 10, 11, 15, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 20, 5, 10, 5, 10, 10, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["torch.onnx._internal.registration", "torch.onnx._globals", "torch.onnx._internal", "collections.abc", "torch._C", "__future__", "dataclasses", "re", "typing", "torch", "builtins", "_frozen_importlib", "abc", "enum", "torch._tensor"], "hash": "6b0f51960b6713591b53e34719298c9ddd7e5ec8", "id": "torch.onnx._internal.jit_utils", "ignore_all": true, "interface_hash": "d47d1d46cb9c40a1c0330c26b498c4130979ba54", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\jit_utils.py", "plugin_data": null, "size": 14503, "suppressed": [], "version_id": "1.16.1"}