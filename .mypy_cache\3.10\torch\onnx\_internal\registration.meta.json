{"data_mtime": 1752081394, "dep_lines": [8, 8, 5, 8, 4, 6, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 20, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["torch.onnx._constants", "torch.onnx.errors", "collections.abc", "torch.onnx", "warnings", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing_extensions"], "hash": "40d0c373219a6a1e44ef2c74cd51b82c5b758ca5", "id": "torch.onnx._internal.registration", "ignore_all": true, "interface_hash": "481bca30c95e028dc5acb64ec2d0ba2b88748c76", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\registration.py", "plugin_data": null, "size": 11357, "suppressed": [], "version_id": "1.16.1"}