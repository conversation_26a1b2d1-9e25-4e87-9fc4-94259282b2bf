{"data_mtime": 1752081394, "dep_lines": [21, 27, 20, 20, 17, 18, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 20, 10, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["transformers.models.auto.auto_factory", "transformers.models.auto.configuration_auto", "transformers.utils.logging", "transformers.utils", "warnings", "collections", "builtins", "_frozen_importlib", "_typeshed", "abc", "logging", "typing"], "hash": "f8953253842929b4010d947fed9ddaaa3e67cd06", "id": "transformers.models.auto.modeling_auto", "ignore_all": true, "interface_hash": "0503d6a9b66c42becb599fcc6a215977617935aa", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\models\\auto\\modeling_auto.py", "plugin_data": null, "size": 89046, "suppressed": [], "version_id": "1.16.1"}