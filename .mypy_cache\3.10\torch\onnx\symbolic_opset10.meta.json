{"data_mtime": 1752081394, "dep_lines": [24, 24, 11, 16, 16, 16, 16, 16, 23, 24, 11, 12, 28, 3, 5, 6, 7, 8, 10, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 5, 20, 10, 10, 25, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30], "dependencies": ["torch.onnx._internal.jit_utils", "torch.onnx._internal.registration", "torch._<PERSON>._onnx", "torch.onnx._constants", "torch.onnx._type_utils", "torch.onnx.errors", "torch.onnx.symbolic_helper", "torch.onnx.symbolic_opset9", "torch.onnx._globals", "torch.onnx._internal", "torch._C", "torch.onnx", "collections.abc", "__future__", "functools", "sys", "warnings", "typing", "torch", "builtins", "_frozen_importlib", "abc", "torch._tensor"], "hash": "0352c02379106fb1616e45978fdba944ad3c8909", "id": "torch.onnx.symbolic_opset10", "ignore_all": true, "interface_hash": "d1c22c01e0c4790424c652ac752e56f2062de16b", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\symbolic_opset10.py", "plugin_data": null, "size": 38616, "suppressed": [], "version_id": "1.16.1"}