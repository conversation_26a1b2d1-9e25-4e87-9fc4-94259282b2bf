{"data_mtime": 1752081394, "dep_lines": [15, 15, 14, 16, 16, 13, 16, 13, 12, 2, 4, 5, 6, 11, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 8], "dep_prios": [10, 10, 10, 10, 10, 10, 20, 20, 10, 5, 10, 10, 5, 10, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["torch.onnx._internal.diagnostics.infra.decorator", "torch.onnx._internal.diagnostics.infra.formatter", "torch.onnx._internal.diagnostics.infra", "torch.onnx._internal.fx.registration", "torch.onnx._internal.fx.type_utils", "torch.onnx._internal.diagnostics", "torch.onnx._internal.fx", "torch.onnx._internal", "torch.fx", "__future__", "dataclasses", "functools", "typing", "torch", "logging", "builtins", "_frozen_importlib", "abc", "enum", "torch._C", "torch._logging", "torch._logging._internal", "torch._tensor", "torch.fx.graph_module", "torch.fx.node", "torch.nn", "torch.nn.modules", "torch.nn.modules.module", "torch.nn.parameter", "torch.onnx._internal.diagnostics._rules", "torch.onnx._internal.diagnostics.infra._infra", "torch.onnx._internal.diagnostics.infra.context"], "hash": "a92ab7e90cdcd9a013a28eb44ec7bb4b685b01be", "id": "torch.onnx._internal.fx.diagnostics", "ignore_all": true, "interface_hash": "34e29f897fbc73f300aafee8081c2c5fef10f1ed", "mtime": 1752005447, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\torch\\onnx\\_internal\\fx\\diagnostics.py", "plugin_data": null, "size": 9196, "suppressed": ["onnxscript.function_libs.torch_lib", "onnxscript"], "version_id": "1.16.1"}