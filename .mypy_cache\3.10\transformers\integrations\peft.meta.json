{"data_mtime": 1752081391, "dep_lines": [23, 21, 23, 15, 16, 17, 18, 19, 21, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 377, 38, 191, 37, 190], "dep_prios": [10, 10, 5, 10, 10, 10, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 5, 20, 5, 20], "dependencies": ["transformers.utils.logging", "packaging.version", "transformers.utils", "importlib", "inspect", "re", "warnings", "typing", "packaging", "torch", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "importlib.metadata", "logging", "os", "torch._C", "torch._tensor", "transformers.utils.import_utils", "transformers.utils.peft_utils", "types", "typing_extensions"], "hash": "936a61d2ee241a5f31157d17cd52562bb8339a3c", "id": "transformers.integrations.peft", "ignore_all": true, "interface_hash": "9e7a7d9b2137dc3b5e113492b5821631cecc1abd", "mtime": 1752005479, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\miniconda3\\envs\\setupagent-clean\\lib\\site-packages\\transformers\\integrations\\peft.py", "plugin_data": null, "size": 28761, "suppressed": ["peft.tuners.tuners_utils", "accelerate.utils", "peft.utils", "accelerate", "peft"], "version_id": "1.16.1"}